<?php
/**
 * Setup Product Categories for PC Market
 * Run this script to create the product category structure
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    die('WooCommerce is not installed or activated.');
}

/**
 * Product Categories Structure based on pcmarket.vn
 */
$categories = array(
    // Main Categories
    'PC Gaming, Streaming' => array(
        'slug' => 'pc-gaming-streaming',
        'description' => 'Máy tính chơi game và streaming chuyên nghiệp',
        'children' => array(
            'PC Gaming Giá Rẻ' => array('slug' => 'pc-gaming-gia-re', 'description' => 'PC Gaming dưới 15 triệu'),
            'PC Gaming Trung Cấp' => array('slug' => 'pc-gaming-trung-cap', 'description' => 'PC Gaming 15-30 triệu'),
            'PC Gaming Cao Cấp' => array('slug' => 'pc-gaming-cao-cap', 'description' => 'PC Gaming trên 30 triệu'),
            'PC Streamer' => array('slug' => 'pc-streamer', 'description' => 'PC chuyên dụng cho streaming'),
            'PC Gaming DDR5' => array('slug' => 'pc-gaming-ddr5', 'description' => 'PC Gaming với RAM DDR5'),
        )
    ),
    
    'PC Workstation' => array(
        'slug' => 'pc-workstation',
        'description' => 'Máy tính workstation cho công việc chuyên nghiệp',
        'children' => array(
            'PC Workstation 2D' => array('slug' => 'pc-workstation-2d', 'description' => 'Workstation cho thiết kế 2D'),
            'PC Workstation 3D' => array('slug' => 'pc-workstation-3d', 'description' => 'Workstation cho thiết kế 3D'),
            'PC Dựng Phim' => array('slug' => 'pc-dung-phim', 'description' => 'PC chuyên dụng edit video'),
        )
    ),
    
    'PC Văn Phòng' => array(
        'slug' => 'pc-van-phong',
        'description' => 'Máy tính văn phòng giá tốt',
        'children' => array(
            'PC Văn Phòng Giá Rẻ' => array('slug' => 'pc-van-phong-gia-re', 'description' => 'PC văn phòng dưới 10 triệu'),
            'PC Văn Phòng Cao Cấp' => array('slug' => 'pc-van-phong-cao-cap', 'description' => 'PC văn phòng cao cấp'),
        )
    ),
    
    'Linh Kiện Máy Tính' => array(
        'slug' => 'linh-kien-may-tinh',
        'description' => 'Linh kiện máy tính chính hãng',
        'children' => array(
            'CPU - Bộ Vi Xử Lý' => array(
                'slug' => 'cpu-bo-vi-xu-ly',
                'description' => 'CPU Intel và AMD',
                'children' => array(
                    'CPU Intel' => array(
                        'slug' => 'cpu-intel',
                        'description' => 'CPU Intel các dòng',
                        'children' => array(
                            'Intel Core i3' => array('slug' => 'intel-core-i3'),
                            'Intel Core i5' => array('slug' => 'intel-core-i5'),
                            'Intel Core i7' => array('slug' => 'intel-core-i7'),
                            'Intel Core i9' => array('slug' => 'intel-core-i9'),
                            'Intel Core Ultra' => array('slug' => 'intel-core-ultra'),
                        )
                    ),
                    'CPU AMD' => array(
                        'slug' => 'cpu-amd',
                        'description' => 'CPU AMD Ryzen',
                        'children' => array(
                            'AMD Ryzen 3' => array('slug' => 'amd-ryzen-3'),
                            'AMD Ryzen 5' => array('slug' => 'amd-ryzen-5'),
                            'AMD Ryzen 7' => array('slug' => 'amd-ryzen-7'),
                            'AMD Ryzen 9' => array('slug' => 'amd-ryzen-9'),
                        )
                    ),
                )
            ),
            
            'VGA - Card Màn Hình' => array(
                'slug' => 'vga-card-man-hinh',
                'description' => 'Card đồ họa NVIDIA và AMD',
                'children' => array(
                    'VGA NVIDIA' => array(
                        'slug' => 'vga-nvidia',
                        'description' => 'Card đồ họa NVIDIA RTX',
                        'children' => array(
                            'RTX 5090' => array('slug' => 'rtx-5090'),
                            'RTX 5080' => array('slug' => 'rtx-5080'),
                            'RTX 5070 Ti' => array('slug' => 'rtx-5070-ti'),
                            'RTX 5070' => array('slug' => 'rtx-5070'),
                            'RTX 5060 Ti' => array('slug' => 'rtx-5060-ti'),
                            'RTX 5060' => array('slug' => 'rtx-5060'),
                            'RTX 4090' => array('slug' => 'rtx-4090'),
                            'RTX 4080' => array('slug' => 'rtx-4080'),
                            'RTX 4070 Ti' => array('slug' => 'rtx-4070-ti'),
                            'RTX 4070' => array('slug' => 'rtx-4070'),
                            'RTX 4060 Ti' => array('slug' => 'rtx-4060-ti'),
                            'RTX 4060' => array('slug' => 'rtx-4060'),
                        )
                    ),
                    'VGA AMD' => array(
                        'slug' => 'vga-amd',
                        'description' => 'Card đồ họa AMD Radeon',
                        'children' => array(
                            'RX 9070 XT' => array('slug' => 'rx-9070-xt'),
                            'RX 9070' => array('slug' => 'rx-9070'),
                            'RX 7900 XTX' => array('slug' => 'rx-7900-xtx'),
                            'RX 7900 XT' => array('slug' => 'rx-7900-xt'),
                            'RX 7800 XT' => array('slug' => 'rx-7800-xt'),
                            'RX 7700 XT' => array('slug' => 'rx-7700-xt'),
                            'RX 7600' => array('slug' => 'rx-7600'),
                        )
                    ),
                )
            ),
            
            'RAM - Bộ Nhớ Trong' => array(
                'slug' => 'ram-bo-nho-trong',
                'description' => 'RAM DDR4 và DDR5',
                'children' => array(
                    'RAM DDR4' => array('slug' => 'ram-ddr4'),
                    'RAM DDR5' => array('slug' => 'ram-ddr5'),
                    'RAM 8GB' => array('slug' => 'ram-8gb'),
                    'RAM 16GB' => array('slug' => 'ram-16gb'),
                    'RAM 32GB' => array('slug' => 'ram-32gb'),
                    'RAM 64GB' => array('slug' => 'ram-64gb'),
                )
            ),
            
            'Mainboard - Bo Mạch Chủ' => array(
                'slug' => 'mainboard-bo-mach-chu',
                'description' => 'Mainboard Intel và AMD',
                'children' => array(
                    'Mainboard Intel' => array('slug' => 'mainboard-intel'),
                    'Mainboard AMD' => array('slug' => 'mainboard-amd'),
                )
            ),
            
            'Ổ Cứng SSD' => array(
                'slug' => 'o-cung-ssd',
                'description' => 'Ổ cứng SSD các loại',
                'children' => array(
                    'SSD SATA' => array('slug' => 'ssd-sata'),
                    'SSD NVMe' => array('slug' => 'ssd-nvme'),
                    'SSD 250GB' => array('slug' => 'ssd-250gb'),
                    'SSD 500GB' => array('slug' => 'ssd-500gb'),
                    'SSD 1TB' => array('slug' => 'ssd-1tb'),
                    'SSD 2TB' => array('slug' => 'ssd-2tb'),
                )
            ),
            
            'PSU - Nguồn Máy Tính' => array(
                'slug' => 'psu-nguon-may-tinh',
                'description' => 'Nguồn máy tính các công suất',
                'children' => array(
                    'Nguồn 500W' => array('slug' => 'nguon-500w'),
                    'Nguồn 650W' => array('slug' => 'nguon-650w'),
                    'Nguồn 750W' => array('slug' => 'nguon-750w'),
                    'Nguồn 850W' => array('slug' => 'nguon-850w'),
                    'Nguồn 1000W+' => array('slug' => 'nguon-1000w'),
                )
            ),
            
            'Case - Vỏ Máy Tính' => array(
                'slug' => 'case-vo-may-tinh',
                'description' => 'Vỏ case máy tính',
                'children' => array(
                    'Case Mini ITX' => array('slug' => 'case-mini-itx'),
                    'Case Micro ATX' => array('slug' => 'case-micro-atx'),
                    'Case Mid Tower' => array('slug' => 'case-mid-tower'),
                    'Case Full Tower' => array('slug' => 'case-full-tower'),
                )
            ),
        )
    ),
    
    'Màn Hình Máy Tính' => array(
        'slug' => 'man-hinh-may-tinh',
        'description' => 'Màn hình gaming và văn phòng',
        'children' => array(
            'Màn Hình Gaming' => array('slug' => 'man-hinh-gaming'),
            'Màn Hình Văn Phòng' => array('slug' => 'man-hinh-van-phong'),
            'Màn Hình 24 inch' => array('slug' => 'man-hinh-24-inch'),
            'Màn Hình 27 inch' => array('slug' => 'man-hinh-27-inch'),
            'Màn Hình 32 inch' => array('slug' => 'man-hinh-32-inch'),
            'Màn Hình 144Hz' => array('slug' => 'man-hinh-144hz'),
            'Màn Hình 165Hz' => array('slug' => 'man-hinh-165hz'),
            'Màn Hình 240Hz' => array('slug' => 'man-hinh-240hz'),
        )
    ),
    
    'Gaming Gear' => array(
        'slug' => 'gaming-gear',
        'description' => 'Phụ kiện gaming chuyên nghiệp',
        'children' => array(
            'Bàn Phím Gaming' => array('slug' => 'ban-phim-gaming'),
            'Chuột Gaming' => array('slug' => 'chuot-gaming'),
            'Tai Nghe Gaming' => array('slug' => 'tai-nghe-gaming'),
            'Ghế Gaming' => array('slug' => 'ghe-gaming'),
            'Bàn Gaming' => array('slug' => 'ban-gaming'),
        )
    ),
);

/**
 * Function to create categories recursively
 */
function create_categories($categories, $parent_id = 0) {
    foreach ($categories as $name => $data) {
        $slug = $data['slug'];
        $description = isset($data['description']) ? $data['description'] : '';
        
        // Check if category already exists
        $existing = term_exists($slug, 'product_cat');
        
        if (!$existing) {
            $result = wp_insert_term(
                $name,
                'product_cat',
                array(
                    'slug' => $slug,
                    'description' => $description,
                    'parent' => $parent_id
                )
            );
            
            if (is_wp_error($result)) {
                echo "Error creating category '$name': " . $result->get_error_message() . "\n";
                continue;
            }
            
            $term_id = $result['term_id'];
            echo "Created category: $name (ID: $term_id)\n";
        } else {
            $term_id = $existing['term_id'];
            echo "Category already exists: $name (ID: $term_id)\n";
        }
        
        // Create child categories if they exist
        if (isset($data['children'])) {
            create_categories($data['children'], $term_id);
        }
    }
}

// Create the categories
echo "Creating product categories...\n";
create_categories($categories);
echo "Category creation completed!\n";
?>
