<?php
/**
 * Vietnam Payment and Shipping Configuration
 * Configure WooCommerce for Vietnamese market
 */

require_once('wp-config.php');
require_once('wp-load.php');

if (!class_exists('WooCommerce')) {
    die('WooCommerce is not installed or activated.');
}

/**
 * Configure Vietnamese Currency
 */
function setup_vietnamese_currency() {
    // Set currency to VND
    update_option('woocommerce_currency', 'VND');
    update_option('woocommerce_currency_pos', 'right_space');
    update_option('woocommerce_price_thousand_sep', ',');
    update_option('woocommerce_price_decimal_sep', '.');
    update_option('woocommerce_price_num_decimals', 0);
    
    echo "✅ Currency set to Vietnamese Dong (VND)\n";
}

/**
 * Configure Payment Methods for Vietnam
 */
function setup_payment_methods() {
    // Enable Cash on Delivery (COD)
    update_option('woocommerce_cod_settings', array(
        'enabled' => 'yes',
        'title' => 'Thanh toán khi nhận hàng (COD)',
        'description' => 'Thanh toán bằng tiền mặt khi nhận hàng. Phí COD: 20,000 VNĐ.',
        'instructions' => 'Vui lòng chuẩn bị tiền mặt để thanh toán khi nhận hàng.',
        'enable_for_methods' => array(),
        'enable_for_virtual' => 'no'
    ));
    
    // Enable Bank Transfer
    update_option('woocommerce_bacs_settings', array(
        'enabled' => 'yes',
        'title' => 'Chuyển khoản ngân hàng',
        'description' => 'Chuyển khoản trực tiếp vào tài khoản ngân hàng của chúng tôi.',
        'instructions' => 'Vui lòng chuyển khoản vào một trong các tài khoản sau và gửi ảnh chụp biên lai để xác nhận.',
        'account_details' => array(
            array(
                'account_name' => 'CÔNG TY TNHH PC MARKET',
                'account_number' => '**********',
                'bank_name' => 'Ngân hàng Vietcombank',
                'sort_code' => '',
                'iban' => '',
                'bic' => ''
            ),
            array(
                'account_name' => 'CÔNG TY TNHH PC MARKET',
                'account_number' => '**********',
                'bank_name' => 'Ngân hàng Techcombank',
                'sort_code' => '',
                'iban' => '',
                'bic' => ''
            )
        )
    ));
    
    echo "✅ Payment methods configured for Vietnam\n";
}

/**
 * Configure Shipping Zones for Vietnam
 */
function setup_shipping_zones() {
    // Delete existing zones first
    $zones = WC_Shipping_Zones::get_zones();
    foreach ($zones as $zone) {
        WC_Shipping_Zones::delete_zone($zone['zone_id']);
    }
    
    // Create Hanoi shipping zone
    $hanoi_zone = new WC_Shipping_Zone();
    $hanoi_zone->set_zone_name('Hà Nội');
    $hanoi_zone->set_zone_order(1);
    $hanoi_zone->save();
    
    // Add Hanoi locations
    $hanoi_zone->add_location('VN:HN', 'state');
    $hanoi_zone->save();
    
    // Add shipping methods for Hanoi
    $hanoi_zone->add_shipping_method('flat_rate');
    $hanoi_methods = $hanoi_zone->get_shipping_methods();
    foreach ($hanoi_methods as $method) {
        if ($method->id === 'flat_rate') {
            $method->update_option('title', 'Giao hàng nội thành Hà Nội');
            $method->update_option('cost', '0'); // Free shipping
            $method->update_option('class_costs', '');
        }
    }
    
    // Create Ho Chi Minh City shipping zone
    $hcm_zone = new WC_Shipping_Zone();
    $hcm_zone->set_zone_name('TP. Hồ Chí Minh');
    $hcm_zone->set_zone_order(2);
    $hcm_zone->save();
    
    // Add HCM locations
    $hcm_zone->add_location('VN:SG', 'state');
    $hcm_zone->save();
    
    // Add shipping methods for HCM
    $hcm_zone->add_shipping_method('flat_rate');
    $hcm_methods = $hcm_zone->get_shipping_methods();
    foreach ($hcm_methods as $method) {
        if ($method->id === 'flat_rate') {
            $method->update_option('title', 'Giao hàng nội thành TP.HCM');
            $method->update_option('cost', '0'); // Free shipping
            $method->update_option('class_costs', '');
        }
    }
    
    // Create nationwide shipping zone
    $vietnam_zone = new WC_Shipping_Zone();
    $vietnam_zone->set_zone_name('Toàn Quốc');
    $vietnam_zone->set_zone_order(3);
    $vietnam_zone->save();
    
    // Add all Vietnam locations
    $vietnam_zone->add_location('VN', 'country');
    $vietnam_zone->save();
    
    // Add shipping methods for nationwide
    $vietnam_zone->add_shipping_method('flat_rate');
    $vietnam_methods = $vietnam_zone->get_shipping_methods();
    foreach ($vietnam_methods as $method) {
        if ($method->id === 'flat_rate') {
            $method->update_option('title', 'Giao hàng toàn quốc');
            $method->update_option('cost', '50000'); // 50,000 VND shipping fee
            $method->update_option('class_costs', '');
        }
    }
    
    echo "✅ Shipping zones configured for Vietnam\n";
}

/**
 * Configure Store Settings
 */
function setup_store_settings() {
    // Store address
    update_option('woocommerce_store_address', '83-85 Thái Hà, Trung Liệt');
    update_option('woocommerce_store_address_2', '');
    update_option('woocommerce_store_city', 'Hà Nội');
    update_option('woocommerce_default_country', 'VN:HN');
    update_option('woocommerce_store_postcode', '100000');
    
    // Currency and locale
    update_option('woocommerce_currency', 'VND');
    update_option('woocommerce_currency_pos', 'right_space');
    update_option('woocommerce_price_thousand_sep', ',');
    update_option('woocommerce_price_decimal_sep', '.');
    update_option('woocommerce_price_num_decimals', 0);
    
    // Tax settings
    update_option('woocommerce_calc_taxes', 'no'); // Disable tax for simplicity
    
    // Inventory settings
    update_option('woocommerce_manage_stock', 'yes');
    update_option('woocommerce_notify_low_stock', 'yes');
    update_option('woocommerce_notify_no_stock', 'yes');
    update_option('woocommerce_stock_email_recipient', '<EMAIL>');
    update_option('woocommerce_notify_low_stock_amount', '5');
    update_option('woocommerce_notify_no_stock_amount', '0');
    
    echo "✅ Store settings configured\n";
}

/**
 * Configure Email Settings
 */
function setup_email_settings() {
    // Email settings
    update_option('woocommerce_email_from_name', 'PC Market');
    update_option('woocommerce_email_from_address', '<EMAIL>');
    update_option('woocommerce_email_header_image', '');
    update_option('woocommerce_email_footer_text', 'PC Market - Máy tính chuyên nghiệp | 087.997.9997 | <EMAIL>');
    update_option('woocommerce_email_base_color', '#e74c3c');
    update_option('woocommerce_email_background_color', '#f7f7f7');
    update_option('woocommerce_email_body_background_color', '#ffffff');
    update_option('woocommerce_email_text_color', '#3c3c3c');
    
    echo "✅ Email settings configured\n";
}

/**
 * Configure Checkout Settings
 */
function setup_checkout_settings() {
    // Checkout settings
    update_option('woocommerce_checkout_company_field', 'optional');
    update_option('woocommerce_checkout_address_2_field', 'optional');
    update_option('woocommerce_checkout_phone_field', 'required');
    update_option('woocommerce_registration_generate_username', 'yes');
    update_option('woocommerce_registration_generate_password', 'yes');
    
    // Account settings
    update_option('woocommerce_enable_myaccount_registration', 'yes');
    update_option('woocommerce_enable_checkout_login_reminder', 'yes');
    update_option('woocommerce_enable_guest_checkout', 'yes');
    
    // Terms and conditions
    $terms_page = wp_insert_post(array(
        'post_title' => 'Điều khoản và Điều kiện',
        'post_content' => '
            <h2>Điều khoản sử dụng</h2>
            <p>Chào mừng bạn đến với PC Market. Bằng cách sử dụng website này, bạn đồng ý với các điều khoản sau:</p>
            
            <h3>1. Chính sách bảo hành</h3>
            <ul>
                <li>Tất cả sản phẩm được bảo hành theo chính sách của nhà sản xuất</li>
                <li>Thời gian bảo hành từ 12-36 tháng tùy sản phẩm</li>
                <li>Bảo hành tại trung tâm bảo hành chính hãng</li>
            </ul>
            
            <h3>2. Chính sách đổi trả</h3>
            <ul>
                <li>Đổi trả trong vòng 7 ngày nếu sản phẩm lỗi</li>
                <li>Sản phẩm phải còn nguyên seal, chưa sử dụng</li>
                <li>Khách hàng chịu phí vận chuyển đổi trả</li>
            </ul>
            
            <h3>3. Chính sách giao hàng</h3>
            <ul>
                <li>Giao hàng miễn phí nội thành Hà Nội và TP.HCM</li>
                <li>Giao hàng toàn quốc với phí 50,000 VNĐ</li>
                <li>Thời gian giao hàng 1-3 ngày làm việc</li>
            </ul>
        ',
        'post_status' => 'publish',
        'post_type' => 'page'
    ));
    
    if ($terms_page) {
        update_option('woocommerce_terms_page_id', $terms_page);
    }
    
    echo "✅ Checkout settings configured\n";
}

/**
 * Run all configuration functions
 */
function configure_vietnam_ecommerce() {
    echo "Configuring WooCommerce for Vietnamese market...\n\n";
    
    setup_vietnamese_currency();
    setup_payment_methods();
    setup_shipping_zones();
    setup_store_settings();
    setup_email_settings();
    setup_checkout_settings();
    
    echo "\n🎉 Vietnam ecommerce configuration completed!\n";
    echo "📍 Store Location: Hà Nội, Vietnam\n";
    echo "💰 Currency: Vietnamese Dong (VND)\n";
    echo "🚚 Shipping: Configured for Vietnam\n";
    echo "💳 Payments: COD and Bank Transfer\n";
}

// Run the configuration
configure_vietnam_ecommerce();
?>
