<?php
/**
 * Website Testing Script
 * Test all functionality of the PC Market website
 */

require_once('wp-config.php');
require_once('wp-load.php');

class WebsiteTest {
    private $results = array();
    private $base_url = 'http://localhost:8000';
    
    public function __construct() {
        echo "🧪 Starting PC Market Website Tests\n";
        echo "=====================================\n\n";
    }
    
    /**
     * Test WordPress Installation
     */
    public function testWordPressInstallation() {
        echo "📋 Testing WordPress Installation...\n";
        
        // Check WordPress version
        $wp_version = get_bloginfo('version');
        $this->addResult('WordPress Version', $wp_version, $wp_version >= '6.0');
        
        // Check if database connection works
        global $wpdb;
        $db_test = $wpdb->get_var("SELECT 1");
        $this->addResult('Database Connection', $db_test == 1 ? 'Connected' : 'Failed', $db_test == 1);
        
        // Check theme activation
        $current_theme = wp_get_theme();
        $theme_active = $current_theme->get('Name') === 'PC Market Theme';
        $this->addResult('Custom Theme Active', $current_theme->get('Name'), $theme_active);
        
        echo "\n";
    }
    
    /**
     * Test WooCommerce Setup
     */
    public function testWooCommerceSetup() {
        echo "🛒 Testing WooCommerce Setup...\n";
        
        // Check WooCommerce activation
        $wc_active = class_exists('WooCommerce');
        $this->addResult('WooCommerce Active', $wc_active ? 'Yes' : 'No', $wc_active);
        
        if ($wc_active) {
            // Check WooCommerce version
            $wc_version = WC()->version;
            $this->addResult('WooCommerce Version', $wc_version, version_compare($wc_version, '7.0', '>='));
            
            // Check currency setting
            $currency = get_woocommerce_currency();
            $this->addResult('Currency Setting', $currency, $currency === 'VND');
            
            // Check shop page
            $shop_page_id = wc_get_page_id('shop');
            $shop_page_exists = $shop_page_id > 0;
            $this->addResult('Shop Page Exists', $shop_page_exists ? 'Yes' : 'No', $shop_page_exists);
        }
        
        echo "\n";
    }
    
    /**
     * Test Product Categories
     */
    public function testProductCategories() {
        echo "📂 Testing Product Categories...\n";
        
        // Check main categories
        $main_categories = array(
            'pc-gaming-streaming' => 'PC Gaming, Streaming',
            'pc-workstation' => 'PC Workstation',
            'linh-kien-may-tinh' => 'Linh Kiện Máy Tính',
            'man-hinh-may-tinh' => 'Màn Hình Máy Tính',
            'gaming-gear' => 'Gaming Gear'
        );
        
        foreach ($main_categories as $slug => $name) {
            $category = get_term_by('slug', $slug, 'product_cat');
            $exists = $category !== false;
            $this->addResult("Category: $name", $exists ? 'Exists' : 'Missing', $exists);
        }
        
        // Check total categories count
        $total_categories = wp_count_terms(array('taxonomy' => 'product_cat'));
        $this->addResult('Total Categories', $total_categories, $total_categories > 20);
        
        echo "\n";
    }
    
    /**
     * Test Sample Products
     */
    public function testSampleProducts() {
        echo "🎮 Testing Sample Products...\n";
        
        // Check if products exist
        $products = wc_get_products(array('limit' => -1));
        $product_count = count($products);
        $this->addResult('Total Products', $product_count, $product_count > 0);
        
        if ($product_count > 0) {
            // Check product with specifications
            $sample_product = $products[0];
            $has_specs = get_post_meta($sample_product->get_id(), '_product_specifications', true);
            $this->addResult('Product Specifications', $has_specs ? 'Present' : 'Missing', !empty($has_specs));
            
            // Check product pricing
            $has_price = $sample_product->get_price() > 0;
            $this->addResult('Product Pricing', $has_price ? 'Set' : 'Missing', $has_price);
            
            // Check featured products
            $featured_products = wc_get_featured_product_ids();
            $has_featured = count($featured_products) > 0;
            $this->addResult('Featured Products', count($featured_products), $has_featured);
        }
        
        echo "\n";
    }
    
    /**
     * Test Theme Files
     */
    public function testThemeFiles() {
        echo "🎨 Testing Theme Files...\n";
        
        $theme_dir = get_template_directory();
        $required_files = array(
            'style.css' => 'Main Stylesheet',
            'index.php' => 'Homepage Template',
            'header.php' => 'Header Template',
            'footer.php' => 'Footer Template',
            'functions.php' => 'Functions File',
            'single-product.php' => 'Product Template',
            'woocommerce.php' => 'WooCommerce Template',
            'page-pc-builder.php' => 'PC Builder Template'
        );
        
        foreach ($required_files as $file => $description) {
            $file_exists = file_exists($theme_dir . '/' . $file);
            $this->addResult($description, $file_exists ? 'Exists' : 'Missing', $file_exists);
        }
        
        // Check assets directory
        $assets_dir = $theme_dir . '/assets';
        $assets_exist = is_dir($assets_dir);
        $this->addResult('Assets Directory', $assets_exist ? 'Exists' : 'Missing', $assets_exist);
        
        if ($assets_exist) {
            $js_file = $assets_dir . '/js/main.js';
            $js_exists = file_exists($js_file);
            $this->addResult('JavaScript File', $js_exists ? 'Exists' : 'Missing', $js_exists);
        }
        
        echo "\n";
    }
    
    /**
     * Test Website Responsiveness
     */
    public function testResponsiveness() {
        echo "📱 Testing Website Responsiveness...\n";
        
        // Check viewport meta tag
        $homepage_content = file_get_contents($this->base_url);
        $has_viewport = strpos($homepage_content, 'viewport') !== false;
        $this->addResult('Viewport Meta Tag', $has_viewport ? 'Present' : 'Missing', $has_viewport);
        
        // Check responsive CSS
        $css_file = get_template_directory() . '/style.css';
        if (file_exists($css_file)) {
            $css_content = file_get_contents($css_file);
            $has_media_queries = strpos($css_content, '@media') !== false;
            $this->addResult('Media Queries', $has_media_queries ? 'Present' : 'Missing', $has_media_queries);
            
            $has_mobile_styles = strpos($css_content, 'max-width: 768px') !== false;
            $this->addResult('Mobile Styles', $has_mobile_styles ? 'Present' : 'Missing', $has_mobile_styles);
        }
        
        echo "\n";
    }
    
    /**
     * Test Performance
     */
    public function testPerformance() {
        echo "⚡ Testing Performance...\n";
        
        // Test page load time
        $start_time = microtime(true);
        $response = file_get_contents($this->base_url);
        $load_time = microtime(true) - $start_time;
        
        $fast_load = $load_time < 2.0; // Under 2 seconds
        $this->addResult('Page Load Time', round($load_time, 2) . 's', $fast_load);
        
        // Check if response is received
        $response_received = $response !== false;
        $this->addResult('Server Response', $response_received ? 'OK' : 'Failed', $response_received);
        
        if ($response_received) {
            // Check page size
            $page_size = strlen($response);
            $reasonable_size = $page_size < 500000; // Under 500KB
            $this->addResult('Page Size', round($page_size / 1024, 2) . 'KB', $reasonable_size);
            
            // Check for basic HTML structure
            $has_html = strpos($response, '<html') !== false;
            $has_head = strpos($response, '<head>') !== false;
            $has_body = strpos($response, '<body') !== false;
            
            $valid_html = $has_html && $has_head && $has_body;
            $this->addResult('Valid HTML Structure', $valid_html ? 'Yes' : 'No', $valid_html);
        }
        
        echo "\n";
    }
    
    /**
     * Test PC Builder Tool
     */
    public function testPCBuilderTool() {
        echo "🔧 Testing PC Builder Tool...\n";
        
        // Check if PC Builder page template exists
        $pc_builder_template = get_template_directory() . '/page-pc-builder.php';
        $template_exists = file_exists($pc_builder_template);
        $this->addResult('PC Builder Template', $template_exists ? 'Exists' : 'Missing', $template_exists);
        
        if ($template_exists) {
            $template_content = file_get_contents($pc_builder_template);
            
            // Check for key elements
            $has_component_selection = strpos($template_content, 'component-selection') !== false;
            $this->addResult('Component Selection', $has_component_selection ? 'Present' : 'Missing', $has_component_selection);
            
            $has_build_summary = strpos($template_content, 'build-summary') !== false;
            $this->addResult('Build Summary', $has_build_summary ? 'Present' : 'Missing', $has_build_summary);
            
            $has_preset_builds = strpos($template_content, 'preset-builds') !== false;
            $this->addResult('Preset Builds', $has_preset_builds ? 'Present' : 'Missing', $has_preset_builds);
            
            $has_javascript = strpos($template_content, '<script>') !== false;
            $this->addResult('PC Builder JavaScript', $has_javascript ? 'Present' : 'Missing', $has_javascript);
        }
        
        echo "\n";
    }
    
    /**
     * Test Security Features
     */
    public function testSecurity() {
        echo "🔒 Testing Security Features...\n";
        
        // Check if wp-config.php is properly configured
        $wp_config_exists = file_exists(ABSPATH . 'wp-config.php');
        $this->addResult('WP Config File', $wp_config_exists ? 'Exists' : 'Missing', $wp_config_exists);
        
        // Check debug settings
        $debug_enabled = defined('WP_DEBUG') && WP_DEBUG;
        $this->addResult('Debug Mode', $debug_enabled ? 'Enabled' : 'Disabled', true); // Both are acceptable
        
        // Check if file editing is disabled in production
        $file_edit_disabled = defined('DISALLOW_FILE_EDIT') && DISALLOW_FILE_EDIT;
        $this->addResult('File Edit Protection', $file_edit_disabled ? 'Enabled' : 'Disabled', true);
        
        echo "\n";
    }
    
    /**
     * Add test result
     */
    private function addResult($test, $result, $passed) {
        $status = $passed ? '✅' : '❌';
        echo sprintf("  %s %s: %s\n", $status, $test, $result);
        
        $this->results[] = array(
            'test' => $test,
            'result' => $result,
            'passed' => $passed
        );
    }
    
    /**
     * Run all tests
     */
    public function runAllTests() {
        $this->testWordPressInstallation();
        $this->testWooCommerceSetup();
        $this->testProductCategories();
        $this->testSampleProducts();
        $this->testThemeFiles();
        $this->testResponsiveness();
        $this->testPerformance();
        $this->testPCBuilderTool();
        $this->testSecurity();
        
        $this->showSummary();
    }
    
    /**
     * Show test summary
     */
    private function showSummary() {
        echo "📊 Test Summary\n";
        echo "===============\n";
        
        $total_tests = count($this->results);
        $passed_tests = array_filter($this->results, function($result) {
            return $result['passed'];
        });
        $passed_count = count($passed_tests);
        $failed_count = $total_tests - $passed_count;
        
        echo sprintf("Total Tests: %d\n", $total_tests);
        echo sprintf("✅ Passed: %d\n", $passed_count);
        echo sprintf("❌ Failed: %d\n", $failed_count);
        echo sprintf("Success Rate: %.1f%%\n", ($passed_count / $total_tests) * 100);
        
        if ($failed_count > 0) {
            echo "\n❌ Failed Tests:\n";
            foreach ($this->results as $result) {
                if (!$result['passed']) {
                    echo sprintf("  - %s: %s\n", $result['test'], $result['result']);
                }
            }
        }
        
        echo "\n🎉 Testing completed!\n";
        
        if ($passed_count / $total_tests >= 0.8) {
            echo "✅ Website is ready for use!\n";
        } else {
            echo "⚠️  Some issues need to be addressed before going live.\n";
        }
    }
}

// Run the tests
$tester = new WebsiteTest();
$tester->runAllTests();
?>
