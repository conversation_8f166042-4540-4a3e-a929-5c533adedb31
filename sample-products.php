<?php
/**
 * Sample Products Data for PC Market
 * Based on pcmarket.vn products
 */

require_once('wp-config.php');
require_once('wp-load.php');

if (!class_exists('WooCommerce')) {
    die('WooCommerce is not installed or activated.');
}

$sample_products = array(
    // PC Gaming Systems
    array(
        'name' => 'PC GAMING RTX 5070 - i5 12400F',
        'price' => 25990000,
        'sale_price' => 23990000,
        'description' => 'Máy tính gaming hiệu năng cao với RTX 5070 và i5 12400F, chơi mượt các game AAA ở setting cao-ultra.',
        'short_description' => 'PC Gaming RTX 5070 - Hiệu năng vượt trội cho gaming 1440p',
        'category' => 'pc-gaming-streaming',
        'sku' => 'PCG-RTX5070-I5',
        'stock' => 15,
        'specifications' => '
            <h4>Cấu hình chi tiết:</h4>
            <ul>
                <li><strong>CPU:</strong> Intel Core i5-12400F (6 nhân 12 luồng, up to 4.4GHz)</li>
                <li><strong>VGA:</strong> RTX 5070 12GB GDDR6X</li>
                <li><strong>RAM:</strong> 16GB DDR4 3200MHz</li>
                <li><strong>SSD:</strong> 500GB NVMe Gen4</li>
                <li><strong>Mainboard:</strong> B760M</li>
                <li><strong>PSU:</strong> 750W 80+ Bronze</li>
                <li><strong>Case:</strong> Mid Tower với 3 fan RGB</li>
            </ul>
        ',
        'tags' => array('gaming', 'rtx-5070', 'intel', 'pc-build')
    ),
    
    array(
        'name' => 'PC GAMING RTX 5060 Ti - AMD Ryzen 5 7500F',
        'price' => 22990000,
        'sale_price' => 19990000,
        'description' => 'Cấu hình gaming tối ưu với AMD Ryzen 5 7500F và RTX 5060 Ti, phù hợp cho game 1440p.',
        'short_description' => 'PC Gaming AMD - Hiệu năng tốt, giá cả hợp lý',
        'category' => 'pc-gaming-streaming',
        'sku' => 'PCG-RTX5060TI-R5',
        'stock' => 20,
        'specifications' => '
            <h4>Cấu hình chi tiết:</h4>
            <ul>
                <li><strong>CPU:</strong> AMD Ryzen 5 7500F (6 nhân 12 luồng, up to 5.0GHz)</li>
                <li><strong>VGA:</strong> RTX 5060 Ti 16GB GDDR6</li>
                <li><strong>RAM:</strong> 16GB DDR5 5200MHz</li>
                <li><strong>SSD:</strong> 1TB NVMe Gen4</li>
                <li><strong>Mainboard:</strong> B650M DDR5</li>
                <li><strong>PSU:</strong> 650W 80+ Gold</li>
                <li><strong>Case:</strong> Mid Tower RGB</li>
            </ul>
        ',
        'tags' => array('gaming', 'rtx-5060-ti', 'amd', 'ryzen')
    ),
    
    // Individual Components
    array(
        'name' => 'Intel Core i7-14700K',
        'price' => 12990000,
        'description' => 'CPU Intel thế hệ 14 mạnh mẽ cho gaming và workstation với 20 nhân 28 luồng.',
        'short_description' => 'CPU Intel Core i7-14700K - 20 nhân 28 luồng',
        'category' => 'intel-core-i7',
        'sku' => 'CPU-I7-14700K',
        'stock' => 25,
        'specifications' => '
            <h4>Thông số kỹ thuật:</h4>
            <ul>
                <li><strong>Số nhân:</strong> 20 (8P + 12E)</li>
                <li><strong>Số luồng:</strong> 28</li>
                <li><strong>Tốc độ base:</strong> 3.4GHz</li>
                <li><strong>Tốc độ boost:</strong> 5.6GHz</li>
                <li><strong>Cache:</strong> 33MB</li>
                <li><strong>Socket:</strong> LGA1700</li>
                <li><strong>TDP:</strong> 125W</li>
            </ul>
        ',
        'tags' => array('cpu', 'intel', 'core-i7', 'lga1700')
    ),
    
    array(
        'name' => 'RTX 5070 Ti Super 16GB',
        'price' => 28990000,
        'sale_price' => 26990000,
        'description' => 'Card đồ họa cao cấp RTX 5070 Ti Super với 16GB VRAM, hoàn hảo cho gaming 1440p và 4K.',
        'short_description' => 'RTX 5070 Ti Super - Card đồ họa cao cấp 16GB',
        'category' => 'rtx-5070-ti',
        'sku' => 'VGA-RTX5070TI-16GB',
        'stock' => 12,
        'specifications' => '
            <h4>Thông số kỹ thuật:</h4>
            <ul>
                <li><strong>GPU:</strong> AD103</li>
                <li><strong>VRAM:</strong> 16GB GDDR6X</li>
                <li><strong>Memory Bus:</strong> 256-bit</li>
                <li><strong>Base Clock:</strong> 2340MHz</li>
                <li><strong>Boost Clock:</strong> 2610MHz</li>
                <li><strong>CUDA Cores:</strong> 8448</li>
                <li><strong>TGP:</strong> 285W</li>
            </ul>
        ',
        'tags' => array('vga', 'nvidia', 'rtx-5070-ti', 'gaming')
    ),
    
    array(
        'name' => 'RAM Corsair Vengeance 32GB DDR5-5200',
        'price' => 4990000,
        'description' => 'Bộ nhớ DDR5 cao cấp Corsair Vengeance với hiệu năng vượt trội và RGB đẹp mắt.',
        'short_description' => 'RAM DDR5 32GB Corsair Vengeance RGB',
        'category' => 'ram-ddr5',
        'sku' => 'RAM-CORSAIR-32GB-DDR5',
        'stock' => 30,
        'specifications' => '
            <h4>Thông số kỹ thuật:</h4>
            <ul>
                <li><strong>Dung lượng:</strong> 32GB (2x16GB)</li>
                <li><strong>Loại:</strong> DDR5</li>
                <li><strong>Tốc độ:</strong> 5200MHz</li>
                <li><strong>Timing:</strong> 40-40-40-77</li>
                <li><strong>Điện áp:</strong> 1.25V</li>
                <li><strong>RGB:</strong> Có</li>
            </ul>
        ',
        'tags' => array('ram', 'ddr5', 'corsair', 'rgb')
    ),
    
    // Monitors
    array(
        'name' => 'Monitor Gaming ASUS ROG 27" 165Hz QHD',
        'price' => 8990000,
        'sale_price' => 7990000,
        'description' => 'Màn hình gaming chuyên nghiệp ASUS ROG 27 inch với độ phân giải QHD và tần số quét 165Hz.',
        'short_description' => 'Monitor Gaming ASUS ROG 27" QHD 165Hz',
        'category' => 'man-hinh-gaming',
        'sku' => 'MON-ASUS-ROG-27-165',
        'stock' => 18,
        'specifications' => '
            <h4>Thông số kỹ thuật:</h4>
            <ul>
                <li><strong>Kích thước:</strong> 27 inch</li>
                <li><strong>Độ phân giải:</strong> 2560x1440 (QHD)</li>
                <li><strong>Tấm nền:</strong> IPS</li>
                <li><strong>Tần số quét:</strong> 165Hz</li>
                <li><strong>Thời gian phản hồi:</strong> 1ms</li>
                <li><strong>G-Sync Compatible:</strong> Có</li>
                <li><strong>HDR:</strong> HDR10</li>
            </ul>
        ',
        'tags' => array('monitor', 'gaming', 'asus', '27-inch', '165hz')
    ),
    
    // Gaming Gear
    array(
        'name' => 'Bàn Phím Cơ Logitech G Pro X TKL',
        'price' => 2990000,
        'description' => 'Bàn phím cơ gaming chuyên nghiệp Logitech G Pro X với switch GX và RGB 16.8 triệu màu.',
        'short_description' => 'Bàn phím cơ gaming Logitech G Pro X',
        'category' => 'ban-phim-gaming',
        'sku' => 'KB-LOGITECH-GPROX',
        'stock' => 25,
        'specifications' => '
            <h4>Thông số kỹ thuật:</h4>
            <ul>
                <li><strong>Switch:</strong> GX Blue Clicky</li>
                <li><strong>Layout:</strong> Tenkeyless</li>
                <li><strong>Kết nối:</strong> USB-C</li>
                <li><strong>RGB:</strong> 16.8 triệu màu</li>
                <li><strong>Keycap:</strong> PBT Double-shot</li>
                <li><strong>Tương thích:</strong> Windows, Mac</li>
            </ul>
        ',
        'tags' => array('keyboard', 'mechanical', 'logitech', 'gaming')
    ),
    
    array(
        'name' => 'Chuột Gaming Razer DeathAdder V3 Pro',
        'price' => 1990000,
        'description' => 'Chuột gaming nhẹ Razer DeathAdder V3 Pro với sensor Focus Pro 30K và độ chính xác cao.',
        'short_description' => 'Chuột gaming Razer DeathAdder V3 Pro',
        'category' => 'chuot-gaming',
        'sku' => 'MOUSE-RAZER-DAV3PRO',
        'stock' => 35,
        'specifications' => '
            <h4>Thông số kỹ thuật:</h4>
            <ul>
                <li><strong>Sensor:</strong> Focus Pro 30K</li>
                <li><strong>DPI:</strong> 30,000</li>
                <li><strong>IPS:</strong> 750</li>
                <li><strong>Acceleration:</strong> 50G</li>
                <li><strong>Switches:</strong> Gen-3 Optical</li>
                <li><strong>Kết nối:</strong> USB-C</li>
                <li><strong>Trọng lượng:</strong> 59g</li>
            </ul>
        ',
        'tags' => array('mouse', 'gaming', 'razer', 'wireless')
    ),
);

/**
 * Function to create sample products
 */
function create_sample_products($products) {
    foreach ($products as $product_data) {
        // Check if product already exists
        $existing = get_page_by_title($product_data['name'], OBJECT, 'product');
        if ($existing) {
            echo "Product already exists: " . $product_data['name'] . "\n";
            continue;
        }
        
        // Create product
        $product = new WC_Product_Simple();
        
        $product->set_name($product_data['name']);
        $product->set_description($product_data['description']);
        $product->set_short_description($product_data['short_description']);
        $product->set_sku($product_data['sku']);
        $product->set_regular_price($product_data['price']);
        
        if (isset($product_data['sale_price'])) {
            $product->set_sale_price($product_data['sale_price']);
        }
        
        $product->set_stock_quantity($product_data['stock']);
        $product->set_manage_stock(true);
        $product->set_stock_status('instock');
        $product->set_status('publish');
        
        // Save product
        $product_id = $product->save();
        
        if ($product_id) {
            echo "Created product: " . $product_data['name'] . " (ID: $product_id)\n";
            
            // Add to category
            if (isset($product_data['category'])) {
                $category = get_term_by('slug', $product_data['category'], 'product_cat');
                if ($category) {
                    wp_set_object_terms($product_id, $category->term_id, 'product_cat');
                }
            }
            
            // Add tags
            if (isset($product_data['tags'])) {
                wp_set_object_terms($product_id, $product_data['tags'], 'product_tag');
            }
            
            // Add specifications
            if (isset($product_data['specifications'])) {
                update_post_meta($product_id, '_product_specifications', $product_data['specifications']);
            }
            
            // Set as featured if it's on sale
            if (isset($product_data['sale_price'])) {
                update_post_meta($product_id, '_featured', 'yes');
            }
        } else {
            echo "Failed to create product: " . $product_data['name'] . "\n";
        }
    }
}

// Create sample products
echo "Creating sample products...\n";
create_sample_products($sample_products);
echo "Sample products creation completed!\n";
?>
