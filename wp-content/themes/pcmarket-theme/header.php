<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>
    
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<header class="site-header">
    <!-- Header Top Bar -->
    <div class="header-top">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="contact-info">
                    <span>📞 Hotline: 087.997.9997</span>
                    <span style="margin-left: 20px;">📍 Hà Nội & TP.HCM</span>
                </div>
                <div class="header-links">
                    <a href="#" style="color: #bdc3c7; text-decoration: none; margin-left: 15px;">Đăng nhập</a>
                    <a href="#" style="color: #bdc3c7; text-decoration: none; margin-left: 15px;">Đăng ký</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <div class="header-main">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <div class="logo-section">
                    <a href="<?php echo home_url(); ?>" class="logo">
                        <?php if (has_custom_logo()) : ?>
                            <?php the_custom_logo(); ?>
                        <?php else : ?>
                            PC Market
                        <?php endif; ?>
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <form role="search" method="get" action="<?php echo home_url('/'); ?>">
                        <input type="search" 
                               placeholder="Tìm kiếm sản phẩm..." 
                               value="<?php echo get_search_query(); ?>" 
                               name="s" 
                               title="Tìm kiếm sản phẩm">
                        <button type="submit">🔍</button>
                    </form>
                </div>

                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- PC Builder Tool -->
                    <a href="#" class="pc-builder-link" style="color: #e74c3c; text-decoration: none; font-weight: bold;">
                        🔧 Xây dựng PC
                    </a>

                    <!-- Cart Icon -->
                    <?php if (class_exists('WooCommerce')) : ?>
                        <a href="<?php echo wc_get_cart_url(); ?>" class="cart-icon">
                            🛒
                            <?php if (WC()->cart->get_cart_contents_count() > 0) : ?>
                                <span class="cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                            <?php endif; ?>
                        </a>
                    <?php endif; ?>

                    <!-- User Account -->
                    <a href="<?php echo wp_login_url(); ?>" class="user-icon">
                        👤
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-navigation">
        <div class="container">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_class' => 'nav-menu',
                'container' => false,
                'fallback_cb' => 'pcmarket_fallback_menu'
            ));
            ?>
        </div>
    </nav>
</header>

<?php
// Fallback menu function
function pcmarket_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li><a href="' . home_url() . '">Trang chủ</a></li>';
    
    if (class_exists('WooCommerce')) {
        echo '<li><a href="' . get_permalink(wc_get_page_id('shop')) . '">Sản phẩm</a>';
        
        // Get product categories
        $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
            'parent' => 0
        ));
        
        if (!empty($categories)) {
            echo '<ul class="dropdown">';
            foreach ($categories as $category) {
                echo '<li><a href="' . get_term_link($category) . '">' . $category->name . '</a></li>';
            }
            echo '</ul>';
        }
        echo '</li>';
        
        echo '<li><a href="' . get_permalink(wc_get_page_id('cart')) . '">Giỏ hàng</a></li>';
        echo '<li><a href="' . get_permalink(wc_get_page_id('myaccount')) . '">Tài khoản</a></li>';
    }
    
    echo '<li><a href="#pc-builder">Xây dựng PC</a></li>';
    echo '<li><a href="#contact">Liên hệ</a></li>';
    echo '</ul>';
}
?>
