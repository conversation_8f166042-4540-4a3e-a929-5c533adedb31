<?php
/**
 * PC Builder Tool Page Template
 */

get_header(); ?>

<div class="pc-builder-container">
    <div class="container">
        <div class="pc-builder-header">
            <h1><PERSON><PERSON>ng Cụ Xây Dựng PC</h1>
            <p><PERSON><PERSON><PERSON> c<PERSON>ình PC phù hợp với nhu cầu và ngân sách của bạn</p>
        </div>

        <div class="pc-builder-layout">
            <!-- Component Selection -->
            <div class="component-selection">
                <h2>Chọ<PERSON> Lin<PERSON>ệ<PERSON></h2>
                
                <div class="component-group">
                    <h3>🖥️ CPU - Bộ Vi Xử Lý</h3>
                    <select class="component-selector" data-component-type="cpu" id="cpu-select">
                        <option value="">Chọn CPU...</option>
                        <option value="i7-14700k" data-price="12990000" data-socket="lga1700">Intel Core i7-14700K - 12,990,000 VNĐ</option>
                        <option value="i5-12400f" data-price="6990000" data-socket="lga1700">Intel Core i5-12400F - 6,990,000 VNĐ</option>
                        <option value="r7-7800x3d" data-price="15990000" data-socket="am5">AMD Ryzen 7 7800X3D - 15,990,000 VNĐ</option>
                        <option value="r5-7500f" data-price="5990000" data-socket="am5">AMD Ryzen 5 7500F - 5,990,000 VNĐ</option>
                    </select>
                    <div class="component-info" id="cpu-info"></div>
                </div>

                <div class="component-group">
                    <h3>🔧 Mainboard - Bo Mạch Chủ</h3>
                    <select class="component-selector" data-component-type="mainboard" id="mainboard-select">
                        <option value="">Chọn Mainboard...</option>
                    </select>
                    <div class="component-info" id="mainboard-info"></div>
                </div>

                <div class="component-group">
                    <h3>🎮 VGA - Card Đồ Họa</h3>
                    <select class="component-selector" data-component-type="vga" id="vga-select">
                        <option value="">Chọn VGA...</option>
                        <option value="rtx-5070-ti" data-price="28990000" data-power="285">RTX 5070 Ti Super 16GB - 28,990,000 VNĐ</option>
                        <option value="rtx-5070" data-price="22990000" data-power="220">RTX 5070 12GB - 22,990,000 VNĐ</option>
                        <option value="rtx-5060-ti" data-price="16990000" data-power="165">RTX 5060 Ti 16GB - 16,990,000 VNĐ</option>
                        <option value="rx-7800-xt" data-price="18990000" data-power="263">AMD RX 7800 XT - 18,990,000 VNĐ</option>
                    </select>
                    <div class="component-info" id="vga-info"></div>
                </div>

                <div class="component-group">
                    <h3>💾 RAM - Bộ Nhớ</h3>
                    <select class="component-selector" data-component-type="ram" id="ram-select">
                        <option value="">Chọn RAM...</option>
                        <option value="ddr5-32gb" data-price="4990000" data-type="ddr5">32GB DDR5-5200 - 4,990,000 VNĐ</option>
                        <option value="ddr5-16gb" data-price="2490000" data-type="ddr5">16GB DDR5-5200 - 2,490,000 VNĐ</option>
                        <option value="ddr4-32gb" data-price="3990000" data-type="ddr4">32GB DDR4-3200 - 3,990,000 VNĐ</option>
                        <option value="ddr4-16gb" data-price="1990000" data-type="ddr4">16GB DDR4-3200 - 1,990,000 VNĐ</option>
                    </select>
                    <div class="component-info" id="ram-info"></div>
                </div>

                <div class="component-group">
                    <h3>💿 SSD - Ổ Cứng</h3>
                    <select class="component-selector" data-component-type="ssd" id="ssd-select">
                        <option value="">Chọn SSD...</option>
                        <option value="ssd-1tb-nvme" data-price="2990000">1TB NVMe Gen4 - 2,990,000 VNĐ</option>
                        <option value="ssd-500gb-nvme" data-price="1990000">500GB NVMe Gen4 - 1,990,000 VNĐ</option>
                        <option value="ssd-2tb-nvme" data-price="5990000">2TB NVMe Gen4 - 5,990,000 VNĐ</option>
                    </select>
                    <div class="component-info" id="ssd-info"></div>
                </div>

                <div class="component-group">
                    <h3>⚡ PSU - Nguồn</h3>
                    <select class="component-selector" data-component-type="psu" id="psu-select">
                        <option value="">Chọn Nguồn...</option>
                    </select>
                    <div class="component-info" id="psu-info"></div>
                </div>

                <div class="component-group">
                    <h3>🏠 Case - Vỏ Máy</h3>
                    <select class="component-selector" data-component-type="case" id="case-select">
                        <option value="">Chọn Case...</option>
                        <option value="case-mid-tower" data-price="1990000">Mid Tower RGB - 1,990,000 VNĐ</option>
                        <option value="case-full-tower" data-price="2990000">Full Tower RGB - 2,990,000 VNĐ</option>
                        <option value="case-mini-itx" data-price="2490000">Mini ITX - 2,490,000 VNĐ</option>
                    </select>
                    <div class="component-info" id="case-info"></div>
                </div>
            </div>

            <!-- Build Summary -->
            <div class="build-summary">
                <h2>Tóm Tắt Cấu Hình</h2>
                
                <div class="selected-components">
                    <div class="component-item" id="selected-cpu">
                        <span class="component-type">CPU:</span>
                        <span class="component-name">Chưa chọn</span>
                        <span class="component-price">0 VNĐ</span>
                    </div>
                    
                    <div class="component-item" id="selected-mainboard">
                        <span class="component-type">Mainboard:</span>
                        <span class="component-name">Chưa chọn</span>
                        <span class="component-price">0 VNĐ</span>
                    </div>
                    
                    <div class="component-item" id="selected-vga">
                        <span class="component-type">VGA:</span>
                        <span class="component-name">Chưa chọn</span>
                        <span class="component-price">0 VNĐ</span>
                    </div>
                    
                    <div class="component-item" id="selected-ram">
                        <span class="component-type">RAM:</span>
                        <span class="component-name">Chưa chọn</span>
                        <span class="component-price">0 VNĐ</span>
                    </div>
                    
                    <div class="component-item" id="selected-ssd">
                        <span class="component-type">SSD:</span>
                        <span class="component-name">Chưa chọn</span>
                        <span class="component-price">0 VNĐ</span>
                    </div>
                    
                    <div class="component-item" id="selected-psu">
                        <span class="component-type">PSU:</span>
                        <span class="component-name">Chưa chọn</span>
                        <span class="component-price">0 VNĐ</span>
                    </div>
                    
                    <div class="component-item" id="selected-case">
                        <span class="component-type">Case:</span>
                        <span class="component-name">Chưa chọn</span>
                        <span class="component-price">0 VNĐ</span>
                    </div>
                </div>

                <div class="build-total">
                    <div class="total-price">
                        <strong>Tổng cộng: <span id="total-amount">0 VNĐ</span></strong>
                    </div>
                    
                    <div class="power-consumption">
                        <span>Công suất ước tính: <span id="total-power">0W</span></span>
                    </div>
                </div>

                <div class="compatibility-check">
                    <h3>Kiểm Tra Tương Thích</h3>
                    <div id="compatibility-messages"></div>
                </div>

                <div class="build-actions">
                    <button class="btn-save-build">💾 Lưu Cấu Hình</button>
                    <button class="btn-add-to-cart">🛒 Thêm Tất Cả Vào Giỏ</button>
                    <button class="btn-share-build">📤 Chia Sẻ</button>
                    <button class="btn-reset-build">🔄 Đặt Lại</button>
                </div>
            </div>
        </div>

        <!-- Preset Builds -->
        <div class="preset-builds">
            <h2>Cấu Hình Gợi Ý</h2>
            <div class="preset-grid">
                <div class="preset-card" data-preset="gaming-budget">
                    <h3>Gaming Giá Rẻ</h3>
                    <p>15-20 triệu VNĐ</p>
                    <ul>
                        <li>i5-12400F + RTX 5060</li>
                        <li>16GB DDR4</li>
                        <li>500GB SSD</li>
                    </ul>
                    <button class="load-preset">Tải Cấu Hình</button>
                </div>
                
                <div class="preset-card" data-preset="gaming-mid">
                    <h3>Gaming Trung Cấp</h3>
                    <p>25-30 triệu VNĐ</p>
                    <ul>
                        <li>i7-14700K + RTX 5070</li>
                        <li>32GB DDR5</li>
                        <li>1TB SSD</li>
                    </ul>
                    <button class="load-preset">Tải Cấu Hình</button>
                </div>
                
                <div class="preset-card" data-preset="gaming-high">
                    <h3>Gaming Cao Cấp</h3>
                    <p>40-50 triệu VNĐ</p>
                    <ul>
                        <li>R7-7800X3D + RTX 5070 Ti</li>
                        <li>32GB DDR5</li>
                        <li>2TB SSD</li>
                    </ul>
                    <button class="load-preset">Tải Cấu Hình</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// PC Builder JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const componentSelectors = document.querySelectorAll('.component-selector');
    const totalAmountElement = document.getElementById('total-amount');
    const totalPowerElement = document.getElementById('total-power');
    const compatibilityMessages = document.getElementById('compatibility-messages');
    
    let selectedComponents = {};
    let totalPrice = 0;
    let totalPower = 0;
    
    // Component compatibility data
    const compatibilityData = {
        mainboards: {
            'lga1700': [
                {value: 'b760m-ddr4', name: 'B760M DDR4 - 3,990,000 VNĐ', price: 3990000, ramType: 'ddr4'},
                {value: 'b760m-ddr5', name: 'B760M DDR5 - 4,990,000 VNĐ', price: 4990000, ramType: 'ddr5'},
                {value: 'z790-ddr5', name: 'Z790 DDR5 - 7,990,000 VNĐ', price: 7990000, ramType: 'ddr5'}
            ],
            'am5': [
                {value: 'b650m-ddr5', name: 'B650M DDR5 - 4,490,000 VNĐ', price: 4490000, ramType: 'ddr5'},
                {value: 'x670e-ddr5', name: 'X670E DDR5 - 8,990,000 VNĐ', price: 8990000, ramType: 'ddr5'}
            ]
        },
        psus: [
            {value: 'psu-650w', name: '650W 80+ Gold - 2,990,000 VNĐ', price: 2990000, wattage: 650},
            {value: 'psu-750w', name: '750W 80+ Gold - 3,490,000 VNĐ', price: 3490000, wattage: 750},
            {value: 'psu-850w', name: '850W 80+ Gold - 3,990,000 VNĐ', price: 3990000, wattage: 850},
            {value: 'psu-1000w', name: '1000W 80+ Gold - 4,990,000 VNĐ', price: 4990000, wattage: 1000}
        ]
    };
    
    // Initialize component selection
    componentSelectors.forEach(selector => {
        selector.addEventListener('change', function() {
            updateComponent(this);
            updateCompatibleComponents();
            calculateTotal();
            checkCompatibility();
        });
    });
    
    function updateComponent(selector) {
        const componentType = selector.dataset.componentType;
        const selectedOption = selector.options[selector.selectedIndex];
        
        if (selectedOption.value) {
            selectedComponents[componentType] = {
                value: selectedOption.value,
                name: selectedOption.text,
                price: parseInt(selectedOption.dataset.price) || 0,
                socket: selectedOption.dataset.socket,
                ramType: selectedOption.dataset.ramType,
                power: parseInt(selectedOption.dataset.power) || 0
            };
        } else {
            delete selectedComponents[componentType];
        }
        
        updateSelectedComponentDisplay(componentType);
    }
    
    function updateSelectedComponentDisplay(componentType) {
        const element = document.getElementById(`selected-${componentType}`);
        const component = selectedComponents[componentType];
        
        if (component) {
            element.querySelector('.component-name').textContent = component.name.split(' - ')[0];
            element.querySelector('.component-price').textContent = formatPrice(component.price);
        } else {
            element.querySelector('.component-name').textContent = 'Chưa chọn';
            element.querySelector('.component-price').textContent = '0 VNĐ';
        }
    }
    
    function updateCompatibleComponents() {
        // Update mainboard options based on CPU socket
        if (selectedComponents.cpu) {
            const socket = selectedComponents.cpu.socket;
            const mainboardSelect = document.getElementById('mainboard-select');
            
            // Clear existing options except first
            mainboardSelect.innerHTML = '<option value="">Chọn Mainboard...</option>';
            
            if (compatibilityData.mainboards[socket]) {
                compatibilityData.mainboards[socket].forEach(mb => {
                    const option = document.createElement('option');
                    option.value = mb.value;
                    option.textContent = mb.name;
                    option.dataset.price = mb.price;
                    option.dataset.ramType = mb.ramType;
                    mainboardSelect.appendChild(option);
                });
            }
        }
        
        // Update PSU recommendations based on total power
        updatePSURecommendations();
    }
    
    function updatePSURecommendations() {
        const psuSelect = document.getElementById('psu-select');
        const estimatedPower = calculateEstimatedPower();
        const recommendedWattage = Math.ceil(estimatedPower * 1.3); // 30% headroom
        
        psuSelect.innerHTML = '<option value="">Chọn Nguồn...</option>';
        
        compatibilityData.psus.forEach(psu => {
            const option = document.createElement('option');
            option.value = psu.value;
            option.textContent = psu.name;
            option.dataset.price = psu.price;
            option.dataset.wattage = psu.wattage;
            
            if (psu.wattage >= recommendedWattage) {
                option.textContent += ' (Khuyến nghị)';
                option.style.fontWeight = 'bold';
            }
            
            psuSelect.appendChild(option);
        });
    }
    
    function calculateEstimatedPower() {
        let power = 100; // Base system power
        
        if (selectedComponents.cpu) power += 125; // Average CPU power
        if (selectedComponents.vga) power += selectedComponents.vga.power;
        if (selectedComponents.ram) power += 20; // RAM power
        if (selectedComponents.ssd) power += 10; // SSD power
        
        return power;
    }
    
    function calculateTotal() {
        totalPrice = 0;
        totalPower = calculateEstimatedPower();
        
        Object.values(selectedComponents).forEach(component => {
            totalPrice += component.price;
        });
        
        totalAmountElement.textContent = formatPrice(totalPrice);
        totalPowerElement.textContent = totalPower + 'W';
    }
    
    function checkCompatibility() {
        const messages = [];
        
        // Check CPU and Mainboard compatibility
        if (selectedComponents.cpu && selectedComponents.mainboard) {
            const cpuSocket = selectedComponents.cpu.socket;
            const mainboardSelect = document.getElementById('mainboard-select');
            const selectedMainboard = mainboardSelect.options[mainboardSelect.selectedIndex];
            
            // This is a simplified check - in real implementation, you'd have more detailed compatibility data
            messages.push('✅ CPU và Mainboard tương thích');
        }
        
        // Check RAM and Mainboard compatibility
        if (selectedComponents.ram && selectedComponents.mainboard) {
            messages.push('✅ RAM và Mainboard tương thích');
        }
        
        // Check PSU wattage
        if (selectedComponents.psu) {
            const psuWattage = parseInt(document.getElementById('psu-select').options[document.getElementById('psu-select').selectedIndex].dataset.wattage);
            const recommendedWattage = Math.ceil(totalPower * 1.3);
            
            if (psuWattage >= recommendedWattage) {
                messages.push('✅ Nguồn đủ công suất');
            } else {
                messages.push('⚠️ Nguồn có thể không đủ công suất');
            }
        }
        
        compatibilityMessages.innerHTML = messages.join('<br>');
    }
    
    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    }
    
    // Preset builds functionality
    document.querySelectorAll('.load-preset').forEach(button => {
        button.addEventListener('click', function() {
            const preset = this.closest('.preset-card').dataset.preset;
            loadPresetBuild(preset);
        });
    });
    
    function loadPresetBuild(preset) {
        // Reset all selectors
        componentSelectors.forEach(selector => {
            selector.selectedIndex = 0;
        });
        selectedComponents = {};
        
        // Load preset configuration
        const presets = {
            'gaming-budget': {
                cpu: 'i5-12400f',
                vga: 'rtx-5060-ti',
                ram: 'ddr4-16gb',
                ssd: 'ssd-500gb-nvme',
                case: 'case-mid-tower'
            },
            'gaming-mid': {
                cpu: 'i7-14700k',
                vga: 'rtx-5070',
                ram: 'ddr5-32gb',
                ssd: 'ssd-1tb-nvme',
                case: 'case-mid-tower'
            },
            'gaming-high': {
                cpu: 'r7-7800x3d',
                vga: 'rtx-5070-ti',
                ram: 'ddr5-32gb',
                ssd: 'ssd-2tb-nvme',
                case: 'case-full-tower'
            }
        };
        
        const config = presets[preset];
        if (config) {
            Object.keys(config).forEach(componentType => {
                const selector = document.getElementById(`${componentType}-select`);
                const option = selector.querySelector(`option[value="${config[componentType]}"]`);
                if (option) {
                    selector.value = config[componentType];
                    updateComponent(selector);
                }
            });
            
            updateCompatibleComponents();
            calculateTotal();
            checkCompatibility();
        }
    }
    
    // Build actions
    document.querySelector('.btn-reset-build').addEventListener('click', function() {
        componentSelectors.forEach(selector => {
            selector.selectedIndex = 0;
        });
        selectedComponents = {};
        totalPrice = 0;
        totalPower = 0;
        
        document.querySelectorAll('.component-item').forEach(item => {
            item.querySelector('.component-name').textContent = 'Chưa chọn';
            item.querySelector('.component-price').textContent = '0 VNĐ';
        });
        
        totalAmountElement.textContent = '0 VNĐ';
        totalPowerElement.textContent = '0W';
        compatibilityMessages.innerHTML = '';
    });
    
    document.querySelector('.btn-add-to-cart').addEventListener('click', function() {
        if (Object.keys(selectedComponents).length === 0) {
            alert('Vui lòng chọn ít nhất một linh kiện!');
            return;
        }
        
        alert('Tính năng thêm vào giỏ hàng sẽ được triển khai với WooCommerce!');
    });
    
    document.querySelector('.btn-save-build').addEventListener('click', function() {
        if (Object.keys(selectedComponents).length === 0) {
            alert('Vui lòng chọn ít nhất một linh kiện!');
            return;
        }
        
        const buildData = {
            components: selectedComponents,
            totalPrice: totalPrice,
            totalPower: totalPower,
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('pcBuild', JSON.stringify(buildData));
        alert('Cấu hình đã được lưu!');
    });
});
</script>

<?php get_footer(); ?>
