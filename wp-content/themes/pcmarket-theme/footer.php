<footer class="site-footer">
    <div class="container">
        <div class="footer-content">
            <!-- Company Info -->
            <div class="footer-section">
                <h3>PC Market</h3>
                <p><PERSON><PERSON><PERSON><PERSON> cung cấ<PERSON><PERSON>, linh kiện PC và gaming gear chất lượng cao với giá cả cạnh tranh nhất thị trường.</p>
                <div class="social-links" style="margin-top: 20px;">
                    <a href="#" style="color: #bdc3c7; margin-right: 15px; text-decoration: none;">📘 Facebook</a>
                    <a href="#" style="color: #bdc3c7; margin-right: 15px; text-decoration: none;">📺 YouTube</a>
                    <a href="#" style="color: #bdc3c7; margin-right: 15px; text-decoration: none;">📷 Instagram</a>
                </div>
            </div>

            <!-- Product Categories -->
            <div class="footer-section">
                <h3><PERSON><PERSON></h3>
                <ul>
                    <li><a href="#">PC Gaming</a></li>
                    <li><a href="#">PC Workstation</a></li>
                    <li><a href="#">Linh Kiện PC</a></li>
                    <li><a href="#">Màn Hình</a></li>
                    <li><a href="#">Gaming Gear</a></li>
                    <li><a href="#">Phụ Kiện</a></li>
                </ul>
            </div>

            <!-- Customer Support -->
            <div class="footer-section">
                <h3>Hỗ Trợ Khách Hàng</h3>
                <ul>
                    <li><a href="#">Hướng dẫn mua hàng</a></li>
                    <li><a href="#">Chính sách bảo hành</a></li>
                    <li><a href="#">Chính sách đổi trả</a></li>
                    <li><a href="#">Phương thức thanh toán</a></li>
                    <li><a href="#">Vận chuyển</a></li>
                    <li><a href="#">FAQ</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div class="footer-section">
                <h3>Thông Tin Liên Hệ</h3>
                <div class="contact-info">
                    <p><strong>Showroom Hà Nội:</strong><br>
                    83-85 Thái Hà, Trung Liệt, Đống Đa<br>
                    📞 036.625.8142</p>
                    
                    <p style="margin-top: 15px;"><strong>Showroom TP.HCM:</strong><br>
                    83A Cửu Long, P.15, Q.10<br>
                    📞 098.668.0497</p>
                    
                    <p style="margin-top: 15px;"><strong>Hotline:</strong> 087.997.9997<br>
                    <strong>Email:</strong> <EMAIL></p>
                </div>
            </div>
        </div>

        <!-- Newsletter Signup -->
        <div class="newsletter-section" style="background: #34495e; padding: 30px; border-radius: 10px; margin: 40px 0; text-align: center;">
            <h3 style="color: #e74c3c; margin-bottom: 15px;">Đăng Ký Nhận Tin Khuyến Mãi</h3>
            <p style="margin-bottom: 20px; color: #bdc3c7;">Nhận thông tin về sản phẩm mới và ưu đãi đặc biệt</p>
            <form style="display: flex; max-width: 400px; margin: 0 auto; gap: 10px;">
                <input type="email" placeholder="Nhập email của bạn..." style="flex: 1; padding: 12px; border: none; border-radius: 5px;">
                <button type="submit" style="background: #e74c3c; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer;">Đăng Ký</button>
            </form>
        </div>

        <!-- Payment Methods -->
        <div class="payment-methods" style="text-align: center; margin: 30px 0; padding: 20px; background: #34495e; border-radius: 10px;">
            <h4 style="color: #e74c3c; margin-bottom: 15px;">Phương Thức Thanh Toán</h4>
            <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                <span style="background: white; padding: 10px 15px; border-radius: 5px; color: #333;">💳 Visa</span>
                <span style="background: white; padding: 10px 15px; border-radius: 5px; color: #333;">💳 Mastercard</span>
                <span style="background: white; padding: 10px 15px; border-radius: 5px; color: #333;">🏦 ATM</span>
                <span style="background: white; padding: 10px 15px; border-radius: 5px; color: #333;">📱 MoMo</span>
                <span style="background: white; padding: 10px 15px; border-radius: 5px; color: #333;">💰 COD</span>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <p>&copy; <?php echo date('Y'); ?> PC Market. Tất cả quyền được bảo lưu.</p>
            <p style="margin-top: 10px; font-size: 14px;">
                Giấy phép ĐKKD số 0110441021 - Sở Kế hoạch và Đầu tư TP Hà Nội
            </p>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<button id="back-to-top" style="position: fixed; bottom: 30px; right: 30px; background: #e74c3c; color: white; border: none; width: 50px; height: 50px; border-radius: 50%; cursor: pointer; display: none; z-index: 1000; font-size: 18px;">↑</button>

<script>
// Back to top functionality
window.addEventListener('scroll', function() {
    const backToTop = document.getElementById('back-to-top');
    if (window.pageYOffset > 300) {
        backToTop.style.display = 'block';
    } else {
        backToTop.style.display = 'none';
    }
});

document.getElementById('back-to-top').addEventListener('click', function() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// Add to cart functionality (basic)
document.addEventListener('DOMContentLoaded', function() {
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            if (productId) {
                // WooCommerce AJAX add to cart
                const data = {
                    action: 'woocommerce_add_to_cart',
                    product_id: productId,
                    quantity: 1
                };
                
                // You would implement AJAX call here for WooCommerce
                this.textContent = 'Đã thêm!';
                this.style.background = '#27ae60';
                
                setTimeout(() => {
                    this.textContent = 'Thêm Vào Giỏ';
                    this.style.background = '';
                }, 2000);
            }
        });
    });
});
</script>

<?php wp_footer(); ?>
</body>
</html>
