/*
Theme Name: PC Market Theme
Description: Custom WordPress theme based on pcmarket.vn design for PC building ecommerce
Version: 1.0
Author: PC Builder Team
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
.site-header {
    background: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    background: #2c3e50;
    color: #fff;
    padding: 10px 0;
    font-size: 14px;
}

.header-main {
    padding: 15px 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: #e74c3c;
    text-decoration: none;
}

.search-bar {
    flex: 1;
    max-width: 500px;
    margin: 0 30px;
    position: relative;
}

.search-bar input {
    width: 100%;
    padding: 12px 50px 12px 15px;
    border: 2px solid #e74c3c;
    border-radius: 5px;
    font-size: 16px;
}

.search-bar button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cart-icon, .user-icon {
    position: relative;
    color: #333;
    text-decoration: none;
    font-size: 18px;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Navigation Styles */
.main-navigation {
    background: #34495e;
    padding: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    display: block;
    padding: 15px 20px;
    color: #fff;
    text-decoration: none;
    transition: background 0.3s;
}

.nav-menu a:hover {
    background: #2c3e50;
}

.nav-menu .dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: #2c3e50;
    min-width: 200px;
    display: none;
    z-index: 1000;
}

.nav-menu li:hover .dropdown {
    display: block;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 48px;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 20px;
    margin-bottom: 30px;
}

.cta-button {
    display: inline-block;
    background: #e74c3c;
    color: white;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-size: 18px;
    transition: background 0.3s;
}

.cta-button:hover {
    background: #c0392b;
}

/* Product Grid */
.products-section {
    padding: 60px 0;
    background: white;
}

.section-title {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #2c3e50;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 18px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.product-price {
    font-size: 20px;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 15px;
}

.add-to-cart {
    width: 100%;
    background: #27ae60;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

.add-to-cart:hover {
    background: #219a52;
}

/* Footer */
.site-footer {
    background: #2c3e50;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 20px;
    color: #e74c3c;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Categories Section */
.categories-section {
    padding: 60px 0;
    background: #fff;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.category-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s;
    text-align: center;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.category-info {
    padding: 20px;
}

.category-info h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.category-info p {
    color: #7f8c8d;
    margin-bottom: 15px;
}

.category-link {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
}

.category-link:hover {
    background: #2980b9;
}

/* Product Specifications */
.product-specs, .product-compatibility {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.product-specs h4, .product-compatibility h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

/* Price Display */
.price {
    font-size: 24px;
    font-weight: bold;
    color: #e74c3c;
}

.price del {
    color: #95a5a6;
    font-size: 18px;
}

/* Sale Badge */
.sale-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #e74c3c;
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Single Product Styles */
.single-product-container {
    padding: 40px 0;
    background: #f8f9fa;
}

.product-breadcrumb {
    margin-bottom: 30px;
}

.breadcrumb {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
}

.breadcrumb li {
    margin-right: 10px;
}

.breadcrumb li:after {
    content: ">";
    margin-left: 10px;
    color: #bdc3c7;
}

.breadcrumb li:last-child:after {
    display: none;
}

.breadcrumb a {
    color: #3498db;
    text-decoration: none;
}

.breadcrumb .active {
    color: #7f8c8d;
}

.single-product-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.product-images {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.main-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 10px;
}

.product-thumbnails {
    display: flex;
    gap: 10px;
    overflow-x: auto;
}

.thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 5px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s;
}

.thumbnail.active,
.thumbnail:hover {
    opacity: 1;
    border: 2px solid #e74c3c;
}

.product-summary {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.product-title {
    font-size: 28px;
    color: #2c3e50;
    margin: 0;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 10px;
}

.product-price {
    font-size: 32px;
    font-weight: bold;
    color: #e74c3c;
}

.product-stock .in-stock {
    color: #27ae60;
    font-weight: bold;
}

.product-stock .out-of-stock {
    color: #e74c3c;
    font-weight: bold;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 20px 0;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 2px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.qty-btn {
    background: #f8f9fa;
    border: none;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: 18px;
    transition: background 0.3s;
}

.qty-btn:hover {
    background: #e9ecef;
}

.qty {
    border: none;
    width: 60px;
    height: 40px;
    text-align: center;
    font-size: 16px;
}

.single_add_to_cart_button {
    background: #27ae60;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
    width: 100%;
}

.single_add_to_cart_button:hover {
    background: #219a52;
}

.product-meta {
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.meta-item {
    margin-bottom: 10px;
    font-size: 14px;
}

.meta-item a {
    color: #3498db;
    text-decoration: none;
}

.product-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.product-actions button {
    background: #f8f9fa;
    border: 1px solid #ddd;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.product-actions button:hover {
    background: #e9ecef;
    border-color: #bbb;
}

/* Product Tabs */
.product-tabs {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 40px;
    overflow: hidden;
}

.tab-nav {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.tab-btn {
    background: none;
    border: none;
    padding: 20px 30px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #e9ecef;
}

.tab-btn.active {
    background: white;
    border-bottom-color: #e74c3c;
    color: #e74c3c;
}

.tab-content {
    padding: 40px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.shipping-info ul {
    list-style: none;
    padding: 0;
}

.shipping-info li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.shipping-info li:last-child {
    border-bottom: none;
}

/* Related Products */
.related-products {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.related-products h3 {
    margin-bottom: 30px;
    color: #2c3e50;
    font-size: 24px;
}

.related-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.related-product-card {
    text-align: center;
    transition: transform 0.3s;
}

.related-product-card:hover {
    transform: translateY(-5px);
}

.related-product-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 10px;
}

.related-product-card h4 {
    font-size: 14px;
    margin: 10px 0;
    color: #2c3e50;
}

.related-product-card a {
    text-decoration: none;
    color: inherit;
}

/* WooCommerce Shop Styles */
.shop-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
}

.shop-sidebar {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.filter-group {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.filter-group h4 {
    font-size: 16px;
    margin-bottom: 15px;
    color: #2c3e50;
}

.filter-group label {
    display: block;
    margin-bottom: 10px;
    cursor: pointer;
    font-size: 14px;
}

.price-filter input[type="range"] {
    width: 100%;
    margin-bottom: 10px;
}

.shop-main {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Mobile Menu Styles */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 10px;
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #34495e;
        flex-direction: column;
        z-index: 1000;
    }

    .nav-menu.mobile-active {
        display: flex;
    }

    .single-product-layout {
        grid-template-columns: 1fr;
        gap: 30px;
        padding: 20px;
    }

    .shop-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .shop-sidebar {
        position: static;
        order: 2;
    }

    .tab-nav {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
        padding: 15px 10px;
        font-size: 14px;
    }
}

/* PC Builder Styles */
.pc-builder-container {
    padding: 40px 0;
    background: #f8f9fa;
}

.pc-builder-header {
    text-align: center;
    margin-bottom: 50px;
}

.pc-builder-header h1 {
    font-size: 36px;
    color: #2c3e50;
    margin-bottom: 15px;
}

.pc-builder-header p {
    font-size: 18px;
    color: #7f8c8d;
}

.pc-builder-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    margin-bottom: 60px;
}

.component-selection {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.component-selection h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 24px;
}

.component-group {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.component-group:last-child {
    border-bottom: none;
}

.component-group h3 {
    color: #34495e;
    margin-bottom: 15px;
    font-size: 18px;
}

.component-selector {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    background: white;
    transition: border-color 0.3s;
}

.component-selector:focus {
    border-color: #3498db;
    outline: none;
}

.component-info {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    font-size: 14px;
    color: #666;
    display: none;
}

.build-summary {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.build-summary h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 22px;
}

.selected-components {
    margin-bottom: 25px;
}

.component-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    font-size: 14px;
}

.component-item:last-child {
    border-bottom: none;
}

.component-type {
    font-weight: bold;
    color: #34495e;
    min-width: 80px;
}

.component-name {
    flex: 1;
    margin: 0 15px;
    color: #666;
}

.component-price {
    font-weight: bold;
    color: #e74c3c;
}

.build-total {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.total-price {
    font-size: 20px;
    color: #2c3e50;
    margin-bottom: 10px;
}

.power-consumption {
    font-size: 14px;
    color: #7f8c8d;
}

.compatibility-check {
    margin-bottom: 25px;
}

.compatibility-check h3 {
    font-size: 16px;
    color: #2c3e50;
    margin-bottom: 10px;
}

#compatibility-messages {
    font-size: 14px;
    line-height: 1.6;
}

.build-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.build-actions button {
    padding: 12px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-save-build {
    background: #3498db;
    color: white;
}

.btn-save-build:hover {
    background: #2980b9;
}

.btn-add-to-cart {
    background: #27ae60;
    color: white;
}

.btn-add-to-cart:hover {
    background: #219a52;
}

.btn-share-build {
    background: #f39c12;
    color: white;
}

.btn-share-build:hover {
    background: #e67e22;
}

.btn-reset-build {
    background: #95a5a6;
    color: white;
}

.btn-reset-build:hover {
    background: #7f8c8d;
}

/* Preset Builds */
.preset-builds {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.preset-builds h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 28px;
    text-align: center;
}

.preset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.preset-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    border: 2px solid #eee;
    transition: all 0.3s;
    text-align: center;
}

.preset-card:hover {
    border-color: #3498db;
    transform: translateY(-5px);
}

.preset-card h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 20px;
}

.preset-card p {
    color: #e74c3c;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
}

.preset-card ul {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    text-align: left;
}

.preset-card li {
    padding: 5px 0;
    color: #666;
    font-size: 14px;
}

.preset-card li:before {
    content: "✓ ";
    color: #27ae60;
    font-weight: bold;
}

.load-preset {
    background: #3498db;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background 0.3s;
    width: 100%;
}

.load-preset:hover {
    background: #2980b9;
}

/* PC Builder Mobile Responsive */
@media (max-width: 768px) {
    .pc-builder-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .build-summary {
        position: static;
        order: -1;
    }

    .build-actions {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .preset-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .pc-builder-header h1 {
        font-size: 28px;
    }

    .component-selection,
    .build-summary,
    .preset-builds {
        padding: 20px;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: #27ae60;
}

.notification-error {
    background: #e74c3c;
}

.notification-warning {
    background: #f39c12;
}

.notification-info {
    background: #3498db;
}

/* Search Results Dropdown */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 5px 5px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background 0.3s;
}

.search-result-item:hover {
    background: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.search-result-price {
    color: #e74c3c;
    font-weight: bold;
}

.search-loading,
.no-results,
.search-error {
    padding: 20px;
    text-align: center;
    color: #7f8c8d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .search-bar {
        margin: 0;
        max-width: 100%;
    }

    .nav-menu {
        flex-direction: column;
    }

    .hero-content h1 {
        font-size: 32px;
    }

    .product-grid, .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}
