/*
Theme Name: PC Market Theme
Description: Custom WordPress theme based on pcmarket.vn design for PC building ecommerce
Version: 1.0
Author: PC Builder Team
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
.site-header {
    background: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    background: #2c3e50;
    color: #fff;
    padding: 10px 0;
    font-size: 14px;
}

.header-main {
    padding: 15px 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: #e74c3c;
    text-decoration: none;
}

.search-bar {
    flex: 1;
    max-width: 500px;
    margin: 0 30px;
    position: relative;
}

.search-bar input {
    width: 100%;
    padding: 12px 50px 12px 15px;
    border: 2px solid #e74c3c;
    border-radius: 5px;
    font-size: 16px;
}

.search-bar button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cart-icon, .user-icon {
    position: relative;
    color: #333;
    text-decoration: none;
    font-size: 18px;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Navigation Styles */
.main-navigation {
    background: #34495e;
    padding: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    display: block;
    padding: 15px 20px;
    color: #fff;
    text-decoration: none;
    transition: background 0.3s;
}

.nav-menu a:hover {
    background: #2c3e50;
}

.nav-menu .dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: #2c3e50;
    min-width: 200px;
    display: none;
    z-index: 1000;
}

.nav-menu li:hover .dropdown {
    display: block;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 48px;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 20px;
    margin-bottom: 30px;
}

.cta-button {
    display: inline-block;
    background: #e74c3c;
    color: white;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-size: 18px;
    transition: background 0.3s;
}

.cta-button:hover {
    background: #c0392b;
}

/* Product Grid */
.products-section {
    padding: 60px 0;
    background: white;
}

.section-title {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #2c3e50;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 18px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.product-price {
    font-size: 20px;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 15px;
}

.add-to-cart {
    width: 100%;
    background: #27ae60;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

.add-to-cart:hover {
    background: #219a52;
}

/* Footer */
.site-footer {
    background: #2c3e50;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 20px;
    color: #e74c3c;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Categories Section */
.categories-section {
    padding: 60px 0;
    background: #fff;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.category-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s;
    text-align: center;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.category-info {
    padding: 20px;
}

.category-info h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.category-info p {
    color: #7f8c8d;
    margin-bottom: 15px;
}

.category-link {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
}

.category-link:hover {
    background: #2980b9;
}

/* Product Specifications */
.product-specs, .product-compatibility {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.product-specs h4, .product-compatibility h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

/* Price Display */
.price {
    font-size: 24px;
    font-weight: bold;
    color: #e74c3c;
}

.price del {
    color: #95a5a6;
    font-size: 18px;
}

/* Sale Badge */
.sale-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #e74c3c;
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .search-bar {
        margin: 0;
        max-width: 100%;
    }

    .nav-menu {
        flex-direction: column;
    }

    .hero-content h1 {
        font-size: 32px;
    }

    .product-grid, .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}
