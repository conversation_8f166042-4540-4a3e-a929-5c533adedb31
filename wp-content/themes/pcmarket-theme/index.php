<?php get_header(); ?>

<main class="main-content">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1>PC Market - M<PERSON><PERSON></h1>
                <p><PERSON><PERSON><PERSON> dựng PC Gaming, Workstation và các linh kiện chất lượng cao</p>
                <a href="<?php echo esc_url(get_permalink(wc_get_page_id('shop'))); ?>" class="cta-button">
                    Khám Phá Sản Phẩm
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Categories -->
    <section class="categories-section">
        <div class="container">
            <h2 class="section-title">Danh Mục Sản Phẩm</h2>
            <div class="categories-grid">
                <div class="category-card">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/pc-gaming.jpg" alt="PC Gaming" class="category-image">
                    <div class="category-info">
                        <h3>PC Gaming</h3>
                        <p>M<PERSON>y t<PERSON> chơi game hiệu năng cao</p>
                        <a href="#" class="category-link">Xem Thêm</a>
                    </div>
                </div>
                
                <div class="category-card">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/components.jpg" alt="Linh Kiện" class="category-image">
                    <div class="category-info">
                        <h3>Linh Kiện</h3>
                        <p>CPU, GPU, RAM, Mainboard</p>
                        <a href="#" class="category-link">Xem Thêm</a>
                    </div>
                </div>
                
                <div class="category-card">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/monitors.jpg" alt="Màn Hình" class="category-image">
                    <div class="category-info">
                        <h3>Màn Hình</h3>
                        <p>Màn hình gaming và văn phòng</p>
                        <a href="#" class="category-link">Xem Thêm</a>
                    </div>
                </div>
                
                <div class="category-card">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/gaming-gear.jpg" alt="Gaming Gear" class="category-image">
                    <div class="category-info">
                        <h3>Gaming Gear</h3>
                        <p>Bàn phím, chuột, tai nghe</p>
                        <a href="#" class="category-link">Xem Thêm</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="products-section">
        <div class="container">
            <h2 class="section-title">Sản Phẩm Nổi Bật</h2>
            
            <?php if (class_exists('WooCommerce')): ?>
                <div class="product-grid">
                    <?php
                    $featured_products = wc_get_featured_product_ids();
                    if (!empty($featured_products)) {
                        $args = array(
                            'post_type' => 'product',
                            'posts_per_page' => 8,
                            'post__in' => $featured_products,
                            'meta_query' => array(
                                array(
                                    'key' => '_visibility',
                                    'value' => array('catalog', 'visible'),
                                    'compare' => 'IN'
                                )
                            )
                        );
                    } else {
                        $args = array(
                            'post_type' => 'product',
                            'posts_per_page' => 8,
                            'meta_query' => array(
                                array(
                                    'key' => '_visibility',
                                    'value' => array('catalog', 'visible'),
                                    'compare' => 'IN'
                                )
                            )
                        );
                    }
                    
                    $products = new WP_Query($args);
                    
                    if ($products->have_posts()) :
                        while ($products->have_posts()) : $products->the_post();
                            global $product;
                            ?>
                            <div class="product-card">
                                <a href="<?php the_permalink(); ?>">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('medium', array('class' => 'product-image')); ?>
                                    <?php else : ?>
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/placeholder.jpg" alt="<?php the_title(); ?>" class="product-image">
                                    <?php endif; ?>
                                </a>
                                
                                <div class="product-info">
                                    <h3 class="product-title">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h3>
                                    
                                    <div class="product-price">
                                        <?php echo $product->get_price_html(); ?>
                                    </div>
                                    
                                    <?php if ($product->is_in_stock()) : ?>
                                        <button class="add-to-cart" data-product-id="<?php echo $product->get_id(); ?>">
                                            Thêm Vào Giỏ
                                        </button>
                                    <?php else : ?>
                                        <button class="add-to-cart" disabled>
                                            Hết Hàng
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            <?php else: ?>
                <div class="product-grid">
                    <!-- Sample products for demo -->
                    <div class="product-card">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/sample-pc.jpg" alt="PC Gaming" class="product-image">
                        <div class="product-info">
                            <h3 class="product-title">PC Gaming RTX 4070</h3>
                            <div class="product-price">25.990.000 VNĐ</div>
                            <button class="add-to-cart">Thêm Vào Giỏ</button>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/sample-cpu.jpg" alt="CPU Intel" class="product-image">
                        <div class="product-info">
                            <h3 class="product-title">Intel Core i7-14700K</h3>
                            <div class="product-price">12.990.000 VNĐ</div>
                            <button class="add-to-cart">Thêm Vào Giỏ</button>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/sample-gpu.jpg" alt="GPU RTX" class="product-image">
                        <div class="product-info">
                            <h3 class="product-title">RTX 4070 Ti Super</h3>
                            <div class="product-price">22.990.000 VNĐ</div>
                            <button class="add-to-cart">Thêm Vào Giỏ</button>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/sample-monitor.jpg" alt="Monitor Gaming" class="product-image">
                        <div class="product-info">
                            <h3 class="product-title">Monitor Gaming 27" 144Hz</h3>
                            <div class="product-price">5.990.000 VNĐ</div>
                            <button class="add-to-cart">Thêm Vào Giỏ</button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="<?php echo esc_url(get_permalink(wc_get_page_id('shop'))); ?>" class="cta-button">
                    Xem Tất Cả Sản Phẩm
                </a>
            </div>
        </div>
    </section>

    <!-- PC Builder Section -->
    <section class="pc-builder-section" style="background: #ecf0f1; padding: 60px 0;">
        <div class="container">
            <div style="text-align: center;">
                <h2 class="section-title">Công Cụ Xây Dựng PC</h2>
                <p style="font-size: 18px; margin-bottom: 30px; color: #7f8c8d;">
                    Tạo cấu hình PC phù hợp với nhu cầu và ngân sách của bạn
                </p>
                <a href="#" class="cta-button">Bắt Đầu Xây Dựng PC</a>
            </div>
        </div>
    </section>
</main>

<?php get_footer(); ?>
