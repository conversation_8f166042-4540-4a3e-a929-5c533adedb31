/**
 * PC Market Theme JavaScript
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initializeTheme();
    });

    /**
     * Initialize theme functionality
     */
    function initializeTheme() {
        initMobileMenu();
        initSearchFunctionality();
        initProductFilters();
        initAddToCart();
        initPCBuilder();
        initScrollEffects();
    }

    /**
     * Mobile Menu Toggle
     */
    function initMobileMenu() {
        // Add mobile menu toggle button
        if (!$('.mobile-menu-toggle').length) {
            $('.main-navigation .container').prepend('<button class="mobile-menu-toggle">☰</button>');
        }

        $('.mobile-menu-toggle').on('click', function() {
            $('.nav-menu').toggleClass('mobile-active');
            $(this).toggleClass('active');
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.main-navigation').length) {
                $('.nav-menu').removeClass('mobile-active');
                $('.mobile-menu-toggle').removeClass('active');
            }
        });
    }

    /**
     * Enhanced Search Functionality
     */
    function initSearchFunctionality() {
        const searchInput = $('.search-bar input[type="search"]');
        const searchResults = $('<div class="search-results"></div>');
        
        searchInput.after(searchResults);

        let searchTimeout;
        searchInput.on('input', function() {
            const query = $(this).val();
            
            clearTimeout(searchTimeout);
            
            if (query.length >= 3) {
                searchTimeout = setTimeout(function() {
                    performAjaxSearch(query, searchResults);
                }, 300);
            } else {
                searchResults.hide();
            }
        });

        // Hide search results when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.search-bar').length) {
                searchResults.hide();
            }
        });
    }

    /**
     * AJAX Search
     */
    function performAjaxSearch(query, resultsContainer) {
        $.ajax({
            url: pcmarket_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'pcmarket_ajax_search',
                query: query,
                nonce: pcmarket_ajax.nonce
            },
            beforeSend: function() {
                resultsContainer.html('<div class="search-loading">Đang tìm kiếm...</div>').show();
            },
            success: function(response) {
                if (response.success) {
                    resultsContainer.html(response.data).show();
                } else {
                    resultsContainer.html('<div class="no-results">Không tìm thấy sản phẩm nào</div>').show();
                }
            },
            error: function() {
                resultsContainer.html('<div class="search-error">Lỗi tìm kiếm</div>').show();
            }
        });
    }

    /**
     * Product Filters
     */
    function initProductFilters() {
        $('.filter-toggle').on('click', function() {
            const filterGroup = $(this).next('.filter-options');
            filterGroup.slideToggle();
            $(this).toggleClass('active');
        });

        $('.filter-option input').on('change', function() {
            applyFilters();
        });

        $('.price-range-slider').on('input', function() {
            updatePriceDisplay();
            applyFilters();
        });
    }

    /**
     * Apply Product Filters
     */
    function applyFilters() {
        const filters = {
            categories: [],
            price_min: $('#price-min').val(),
            price_max: $('#price-max').val(),
            brands: [],
            in_stock: $('#in-stock').is(':checked')
        };

        $('.category-filter:checked').each(function() {
            filters.categories.push($(this).val());
        });

        $('.brand-filter:checked').each(function() {
            filters.brands.push($(this).val());
        });

        // Show loading state
        $('.product-grid').addClass('loading');

        $.ajax({
            url: pcmarket_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'pcmarket_filter_products',
                filters: filters,
                nonce: pcmarket_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('.product-grid').html(response.data).removeClass('loading');
                }
            },
            error: function() {
                $('.product-grid').removeClass('loading');
                alert('Lỗi khi lọc sản phẩm');
            }
        });
    }

    /**
     * Enhanced Add to Cart
     */
    function initAddToCart() {
        $(document).on('click', '.add-to-cart', function(e) {
            e.preventDefault();
            
            const button = $(this);
            const productId = button.data('product-id');
            const quantity = button.closest('.product-card').find('.quantity-input').val() || 1;

            if (!productId) {
                alert('Lỗi: Không tìm thấy ID sản phẩm');
                return;
            }

            // Show loading state
            const originalText = button.text();
            button.html('<span class="spinner"></span> Đang thêm...').prop('disabled', true);

            $.ajax({
                url: pcmarket_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'pcmarket_add_to_cart',
                    product_id: productId,
                    quantity: quantity,
                    nonce: pcmarket_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        button.html('✓ Đã thêm').removeClass('add-to-cart').addClass('added-to-cart');
                        updateCartCount(response.data.cart_count);
                        showNotification('Sản phẩm đã được thêm vào giỏ hàng', 'success');
                        
                        // Reset button after 3 seconds
                        setTimeout(function() {
                            button.html(originalText).removeClass('added-to-cart').addClass('add-to-cart').prop('disabled', false);
                        }, 3000);
                    } else {
                        button.html(originalText).prop('disabled', false);
                        showNotification('Không thể thêm sản phẩm vào giỏ hàng', 'error');
                    }
                },
                error: function() {
                    button.html(originalText).prop('disabled', false);
                    showNotification('Lỗi kết nối', 'error');
                }
            });
        });
    }

    /**
     * PC Builder Functionality
     */
    function initPCBuilder() {
        $('.component-selector').on('change', function() {
            const componentType = $(this).data('component-type');
            const selectedValue = $(this).val();
            
            updateCompatibleComponents(componentType, selectedValue);
            calculateTotalPrice();
        });

        $('.pc-builder-reset').on('click', function() {
            resetPCBuilder();
        });

        $('.pc-builder-save').on('click', function() {
            savePCBuild();
        });
    }

    /**
     * Update Compatible Components
     */
    function updateCompatibleComponents(changedComponent, selectedValue) {
        const allSelections = {};
        $('.component-selector').each(function() {
            const type = $(this).data('component-type');
            const value = $(this).val();
            if (value) {
                allSelections[type] = value;
            }
        });

        $.ajax({
            url: pcmarket_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'pcmarket_get_compatible_components',
                changed_component: changedComponent,
                selected_value: selectedValue,
                all_selections: allSelections,
                nonce: pcmarket_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateComponentOptions(response.data);
                }
            }
        });
    }

    /**
     * Calculate Total Price
     */
    function calculateTotalPrice() {
        let total = 0;
        $('.component-selector').each(function() {
            const price = parseFloat($(this).find('option:selected').data('price')) || 0;
            total += price;
        });

        $('.total-price').text(formatPrice(total));
    }

    /**
     * Scroll Effects
     */
    function initScrollEffects() {
        $(window).on('scroll', function() {
            const scrollTop = $(this).scrollTop();
            
            // Sticky header
            if (scrollTop > 100) {
                $('.site-header').addClass('scrolled');
            } else {
                $('.site-header').removeClass('scrolled');
            }

            // Animate elements on scroll
            $('.animate-on-scroll').each(function() {
                const elementTop = $(this).offset().top;
                const windowBottom = $(window).scrollTop() + $(window).height();
                
                if (elementTop < windowBottom - 100) {
                    $(this).addClass('animated');
                }
            });
        });
    }

    /**
     * Utility Functions
     */
    function updateCartCount(count) {
        $('.cart-count').text(count);
        if (count > 0) {
            $('.cart-count').show();
        } else {
            $('.cart-count').hide();
        }
    }

    function showNotification(message, type) {
        const notification = $('<div class="notification notification-' + type + '">' + message + '</div>');
        $('body').append(notification);
        
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 3000);
    }

    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    }

})(jQuery);
