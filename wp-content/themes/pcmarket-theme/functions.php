<?php
/**
 * PC Market Theme Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function pcmarket_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add WooCommerce support
    add_theme_support('woocommerce');
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'pcmarket'),
        'footer' => __('Footer Menu', 'pcmarket'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'pcmarket_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function pcmarket_scripts() {
    // Theme stylesheet
    wp_enqueue_style('pcmarket-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Custom JavaScript
    wp_enqueue_script('pcmarket-script', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);
    
    // WooCommerce specific scripts
    if (class_exists('WooCommerce')) {
        wp_enqueue_script('wc-add-to-cart-variation');
        wp_enqueue_script('wc-single-product');
    }
    
    // Localize script for AJAX
    wp_localize_script('pcmarket-script', 'pcmarket_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('pcmarket_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'pcmarket_scripts');

/**
 * Register Widget Areas
 */
function pcmarket_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'pcmarket'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'pcmarket'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area', 'pcmarket'),
        'id'            => 'footer-widgets',
        'description'   => __('Add widgets here to appear in your footer.', 'pcmarket'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'pcmarket_widgets_init');

/**
 * Custom Post Types
 */
function pcmarket_custom_post_types() {
    // PC Build Configuration Post Type
    register_post_type('pc_build', array(
        'labels' => array(
            'name' => 'PC Builds',
            'singular_name' => 'PC Build',
            'add_new' => 'Add New Build',
            'add_new_item' => 'Add New PC Build',
            'edit_item' => 'Edit PC Build',
            'new_item' => 'New PC Build',
            'view_item' => 'View PC Build',
            'search_items' => 'Search PC Builds',
            'not_found' => 'No PC builds found',
            'not_found_in_trash' => 'No PC builds found in trash'
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'menu_icon' => 'dashicons-desktop',
        'rewrite' => array('slug' => 'pc-builds'),
    ));
}
add_action('init', 'pcmarket_custom_post_types');

/**
 * Custom Taxonomies
 */
function pcmarket_custom_taxonomies() {
    // PC Build Categories
    register_taxonomy('pc_build_category', 'pc_build', array(
        'labels' => array(
            'name' => 'Build Categories',
            'singular_name' => 'Build Category',
            'search_items' => 'Search Categories',
            'all_items' => 'All Categories',
            'edit_item' => 'Edit Category',
            'update_item' => 'Update Category',
            'add_new_item' => 'Add New Category',
            'new_item_name' => 'New Category Name',
            'menu_name' => 'Categories',
        ),
        'hierarchical' => true,
        'public' => true,
        'rewrite' => array('slug' => 'build-category'),
    ));
}
add_action('init', 'pcmarket_custom_taxonomies');

/**
 * WooCommerce Customizations
 */
if (class_exists('WooCommerce')) {
    // Remove WooCommerce default styles
    add_filter('woocommerce_enqueue_styles', '__return_empty_array');
    
    // Custom WooCommerce product loop
    function pcmarket_woocommerce_product_loop_start() {
        echo '<div class="product-grid">';
    }
    
    function pcmarket_woocommerce_product_loop_end() {
        echo '</div>';
    }
    
    // Change number of products per page
    function pcmarket_products_per_page() {
        return 12;
    }
    add_filter('loop_shop_per_page', 'pcmarket_products_per_page', 20);
    
    // Add custom fields to products
    function pcmarket_add_product_custom_fields() {
        global $woocommerce, $post;
        
        echo '<div class="product-custom-fields">';
        
        // Technical specifications
        $specs = get_post_meta($post->ID, '_product_specifications', true);
        if ($specs) {
            echo '<div class="product-specs">';
            echo '<h4>Thông số kỹ thuật:</h4>';
            echo '<div class="specs-content">' . wp_kses_post($specs) . '</div>';
            echo '</div>';
        }
        
        // Compatibility information
        $compatibility = get_post_meta($post->ID, '_product_compatibility', true);
        if ($compatibility) {
            echo '<div class="product-compatibility">';
            echo '<h4>Tương thích:</h4>';
            echo '<div class="compatibility-content">' . wp_kses_post($compatibility) . '</div>';
            echo '</div>';
        }
        
        echo '</div>';
    }
    add_action('woocommerce_single_product_summary', 'pcmarket_add_product_custom_fields', 25);
}

/**
 * AJAX Functions
 */
function pcmarket_ajax_add_to_cart() {
    if (!wp_verify_nonce($_POST['nonce'], 'pcmarket_nonce')) {
        wp_die('Security check failed');
    }
    
    $product_id = intval($_POST['product_id']);
    $quantity = intval($_POST['quantity']);
    
    if (class_exists('WooCommerce')) {
        $result = WC()->cart->add_to_cart($product_id, $quantity);
        
        if ($result) {
            wp_send_json_success(array(
                'message' => 'Product added to cart',
                'cart_count' => WC()->cart->get_cart_contents_count()
            ));
        } else {
            wp_send_json_error('Failed to add product to cart');
        }
    }
    
    wp_die();
}
add_action('wp_ajax_pcmarket_add_to_cart', 'pcmarket_ajax_add_to_cart');
add_action('wp_ajax_nopriv_pcmarket_add_to_cart', 'pcmarket_ajax_add_to_cart');

/**
 * PC Builder Functions
 */
function pcmarket_get_compatible_components($component_type, $selected_components = array()) {
    // This function would return compatible components based on selected parts
    // For now, return all products of the specified type
    
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => '_component_type',
                'value' => $component_type,
                'compare' => '='
            )
        )
    );
    
    return get_posts($args);
}

/**
 * Custom Admin Functions
 */
function pcmarket_add_admin_menu() {
    add_menu_page(
        'PC Builder Settings',
        'PC Builder',
        'manage_options',
        'pc-builder-settings',
        'pcmarket_admin_page',
        'dashicons-desktop',
        30
    );
}
add_action('admin_menu', 'pcmarket_add_admin_menu');

function pcmarket_admin_page() {
    echo '<div class="wrap">';
    echo '<h1>PC Builder Settings</h1>';
    echo '<p>Configure PC Builder tool settings here.</p>';
    echo '</div>';
}

/**
 * Security and Performance
 */
// Remove WordPress version from head
remove_action('wp_head', 'wp_generator');

// Disable XML-RPC
add_filter('xmlrpc_enabled', '__return_false');

// Remove unnecessary scripts
function pcmarket_remove_scripts() {
    if (!is_admin()) {
        wp_deregister_script('wp-embed');
    }
}
add_action('wp_footer', 'pcmarket_remove_scripts');
?>
