<?php
/**
 * Single Product Template
 */

get_header(); ?>

<div class="single-product-container">
    <div class="container">
        <?php while (have_posts()) : the_post(); ?>
            <?php global $product; ?>
            
            <div class="product-breadcrumb">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li><a href="<?php echo home_url(); ?>">Trang chủ</a></li>
                        <li><a href="<?php echo get_permalink(wc_get_page_id('shop')); ?>">Sản phẩm</a></li>
                        <?php
                        $categories = get_the_terms(get_the_ID(), 'product_cat');
                        if ($categories && !is_wp_error($categories)) {
                            $category = array_shift($categories);
                            echo '<li><a href="' . get_term_link($category) . '">' . $category->name . '</a></li>';
                        }
                        ?>
                        <li class="active"><?php the_title(); ?></li>
                    </ol>
                </nav>
            </div>

            <div class="single-product-layout">
                <div class="product-images">
                    <div class="main-image">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('large', array('id' => 'main-product-image')); ?>
                        <?php else : ?>
                            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/placeholder.jpg" alt="<?php the_title(); ?>" id="main-product-image">
                        <?php endif; ?>
                    </div>
                    
                    <?php
                    $attachment_ids = $product->get_gallery_image_ids();
                    if ($attachment_ids) :
                    ?>
                        <div class="product-thumbnails">
                            <?php if (has_post_thumbnail()) : ?>
                                <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'thumbnail'); ?>" alt="<?php the_title(); ?>" class="thumbnail active" data-large="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'large'); ?>">
                            <?php endif; ?>
                            
                            <?php foreach ($attachment_ids as $attachment_id) : ?>
                                <img src="<?php echo wp_get_attachment_image_url($attachment_id, 'thumbnail'); ?>" alt="<?php the_title(); ?>" class="thumbnail" data-large="<?php echo wp_get_attachment_image_url($attachment_id, 'large'); ?>">
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="product-summary">
                    <h1 class="product-title"><?php the_title(); ?></h1>
                    
                    <div class="product-rating">
                        <?php if ($product->get_average_rating()) : ?>
                            <div class="star-rating">
                                <?php echo wc_get_rating_html($product->get_average_rating()); ?>
                            </div>
                            <span class="rating-count">(<?php echo $product->get_review_count(); ?> đánh giá)</span>
                        <?php endif; ?>
                    </div>

                    <div class="product-price">
                        <?php echo $product->get_price_html(); ?>
                    </div>

                    <div class="product-stock">
                        <?php if ($product->is_in_stock()) : ?>
                            <span class="in-stock">✓ Còn hàng</span>
                        <?php else : ?>
                            <span class="out-of-stock">✗ Hết hàng</span>
                        <?php endif; ?>
                    </div>

                    <div class="product-short-description">
                        <?php echo apply_filters('woocommerce_short_description', $product->get_short_description()); ?>
                    </div>

                    <form class="cart" action="<?php echo esc_url(apply_filters('woocommerce_add_to_cart_form_action', $product->get_permalink())); ?>" method="post" enctype='multipart/form-data'>
                        <?php if ($product->is_type('simple')) : ?>
                            <div class="quantity-selector">
                                <label for="quantity">Số lượng:</label>
                                <div class="quantity-controls">
                                    <button type="button" class="qty-btn minus">-</button>
                                    <input type="number" id="quantity" class="input-text qty text" step="1" min="1" max="<?php echo $product->get_max_purchase_quantity(); ?>" name="quantity" value="1" title="Số lượng" size="4" placeholder="" inputmode="numeric">
                                    <button type="button" class="qty-btn plus">+</button>
                                </div>
                            </div>
                        <?php endif; ?>

                        <button type="submit" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>" class="single_add_to_cart_button button">
                            <?php echo esc_html($product->single_add_to_cart_text()); ?>
                        </button>
                    </form>

                    <div class="product-meta">
                        <div class="meta-item">
                            <strong>SKU:</strong> <?php echo $product->get_sku() ? $product->get_sku() : 'N/A'; ?>
                        </div>
                        
                        <?php if ($categories) : ?>
                            <div class="meta-item">
                                <strong>Danh mục:</strong>
                                <?php
                                $category_names = array();
                                foreach ($categories as $category) {
                                    $category_names[] = '<a href="' . get_term_link($category) . '">' . $category->name . '</a>';
                                }
                                echo implode(', ', $category_names);
                                ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php
                        $tags = get_the_terms(get_the_ID(), 'product_tag');
                        if ($tags && !is_wp_error($tags)) :
                        ?>
                            <div class="meta-item">
                                <strong>Tags:</strong>
                                <?php
                                $tag_names = array();
                                foreach ($tags as $tag) {
                                    $tag_names[] = '<a href="' . get_term_link($tag) . '">' . $tag->name . '</a>';
                                }
                                echo implode(', ', $tag_names);
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="product-actions">
                        <button class="wishlist-btn">♡ Thêm vào yêu thích</button>
                        <button class="compare-btn">⚖ So sánh</button>
                        <button class="share-btn">📤 Chia sẻ</button>
                    </div>
                </div>
            </div>

            <div class="product-tabs">
                <nav class="tab-nav">
                    <button class="tab-btn active" data-tab="description">Mô tả</button>
                    <button class="tab-btn" data-tab="specifications">Thông số kỹ thuật</button>
                    <button class="tab-btn" data-tab="reviews">Đánh giá (<?php echo $product->get_review_count(); ?>)</button>
                    <button class="tab-btn" data-tab="shipping">Vận chuyển</button>
                </nav>

                <div class="tab-content">
                    <div id="description" class="tab-pane active">
                        <?php the_content(); ?>
                    </div>

                    <div id="specifications" class="tab-pane">
                        <?php
                        $specifications = get_post_meta(get_the_ID(), '_product_specifications', true);
                        if ($specifications) {
                            echo wp_kses_post($specifications);
                        } else {
                            echo '<p>Thông số kỹ thuật sẽ được cập nhật sớm.</p>';
                        }
                        ?>
                    </div>

                    <div id="reviews" class="tab-pane">
                        <?php comments_template(); ?>
                    </div>

                    <div id="shipping" class="tab-pane">
                        <div class="shipping-info">
                            <h4>Thông tin vận chuyển</h4>
                            <ul>
                                <li>🚚 Giao hàng miễn phí trong nội thành Hà Nội và TP.HCM</li>
                                <li>⏰ Giao hàng trong 2-4 giờ (nội thành)</li>
                                <li>📦 Đóng gói cẩn thận, bảo đảm an toàn</li>
                                <li>💰 Thanh toán khi nhận hàng (COD)</li>
                                <li>🔄 Đổi trả trong 7 ngày</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Products -->
            <div class="related-products">
                <h3>Sản phẩm liên quan</h3>
                <div class="related-products-grid">
                    <?php
                    $related_ids = wc_get_related_products($product->get_id(), 4);
                    if ($related_ids) {
                        $related_products = wc_get_products(array(
                            'include' => $related_ids,
                            'limit' => 4
                        ));
                        
                        foreach ($related_products as $related_product) {
                            echo '<div class="related-product-card">';
                            echo '<a href="' . get_permalink($related_product->get_id()) . '">';
                            echo get_the_post_thumbnail($related_product->get_id(), 'medium');
                            echo '<h4>' . $related_product->get_name() . '</h4>';
                            echo '<div class="price">' . $related_product->get_price_html() . '</div>';
                            echo '</a>';
                            echo '</div>';
                        }
                    }
                    ?>
                </div>
            </div>

        <?php endwhile; ?>
    </div>
</div>

<script>
// Product image gallery
document.querySelectorAll('.thumbnail').forEach(thumb => {
    thumb.addEventListener('click', function() {
        document.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
        document.getElementById('main-product-image').src = this.dataset.large;
    });
});

// Quantity controls
document.querySelector('.minus')?.addEventListener('click', function() {
    const qty = document.getElementById('quantity');
    if (qty.value > 1) qty.value = parseInt(qty.value) - 1;
});

document.querySelector('.plus')?.addEventListener('click', function() {
    const qty = document.getElementById('quantity');
    const max = parseInt(qty.getAttribute('max'));
    if (!max || parseInt(qty.value) < max) qty.value = parseInt(qty.value) + 1;
});

// Product tabs
document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const tabId = this.dataset.tab;
        
        document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
        
        this.classList.add('active');
        document.getElementById(tabId).classList.add('active');
    });
});
</script>

<?php get_footer(); ?>
