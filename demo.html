<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PC Market - <PERSON><PERSON><PERSON></title>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        /* Header Styles */
        .site-header {
            background: #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-top {
            background: #2c3e50;
            color: #fff;
            padding: 10px 0;
            font-size: 14px;
        }

        .header-top .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-main {
            padding: 15px 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
            text-decoration: none;
        }

        .search-bar {
            flex: 1;
            max-width: 500px;
            margin: 0 30px;
            position: relative;
        }

        .search-bar input {
            width: 100%;
            padding: 12px 50px 12px 15px;
            border: 2px solid #e74c3c;
            border-radius: 5px;
            font-size: 16px;
        }

        .search-bar button {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 3px;
            cursor: pointer;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .cart-icon, .user-icon {
            position: relative;
            color: #333;
            text-decoration: none;
            font-size: 18px;
        }

        /* Navigation Styles */
        .main-navigation {
            background: #34495e;
            padding: 0;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            display: block;
            padding: 15px 20px;
            color: #fff;
            text-decoration: none;
            transition: background 0.3s;
        }

        .nav-menu li a:hover {
            background: #2c3e50;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }

        .hero-content h1 {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .hero-content p {
            font-size: 20px;
            margin-bottom: 30px;
        }

        .cta-button {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 18px;
            transition: background 0.3s;
        }

        .cta-button:hover {
            background: #c0392b;
        }

        /* Product Grid */
        .products-section {
            padding: 60px 0;
            background: white;
        }

        .section-title {
            text-align: center;
            font-size: 36px;
            margin-bottom: 50px;
            color: #2c3e50;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .product-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        .product-info {
            padding: 20px;
        }

        .product-title {
            font-size: 18px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .product-price {
            font-size: 20px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 15px;
        }

        .add-to-cart {
            width: 100%;
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .add-to-cart:hover {
            background: #219a52;
        }

        /* PC Builder Section */
        .pc-builder-section {
            background: #ecf0f1;
            padding: 60px 0;
            text-align: center;
        }

        /* Footer */
        .site-footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0 20px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-section h3 {
            margin-bottom: 20px;
            color: #e74c3c;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 10px;
        }

        .footer-section ul li a {
            color: #bdc3c7;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-section ul li a:hover {
            color: white;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #34495e;
            color: #bdc3c7;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-bar {
                margin: 0;
                max-width: 100%;
            }
            
            .nav-menu {
                flex-direction: column;
            }
            
            .hero-content h1 {
                font-size: 32px;
            }
            
            .product-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }
        }
    </style>
</head>
<body>

<header class="site-header">
    <!-- Header Top Bar -->
    <div class="header-top">
        <div class="container">
            <div class="contact-info">
                <span>📞 Hotline: 087.997.9997</span>
                <span style="margin-left: 20px;">📍 Hà Nội & TP.HCM</span>
            </div>
            <div class="header-links">
                <a href="#" style="color: #bdc3c7; text-decoration: none; margin-left: 15px;">Đăng nhập</a>
                <a href="#" style="color: #bdc3c7; text-decoration: none; margin-left: 15px;">Đăng ký</a>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <div class="header-main">
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <a href="#" class="logo">PC Market</a>
                </div>

                <div class="search-bar">
                    <input type="search" placeholder="Tìm kiếm sản phẩm..." />
                    <button type="submit">🔍</button>
                </div>

                <div class="header-actions">
                    <a href="#" style="color: #e74c3c; text-decoration: none; font-weight: bold;">
                        🔧 Xây dựng PC
                    </a>
                    <a href="#" class="cart-icon">🛒</a>
                    <a href="#" class="user-icon">👤</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-navigation">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="#">Trang chủ</a></li>
                <li><a href="#">PC Gaming</a></li>
                <li><a href="#">Linh Kiện</a></li>
                <li><a href="#">Màn Hình</a></li>
                <li><a href="#">Gaming Gear</a></li>
                <li><a href="#">Xây dựng PC</a></li>
                <li><a href="#">Liên hệ</a></li>
            </ul>
        </div>
    </nav>
</header>

<main class="main-content">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1>PC Market - Máy Tính Chuyên Nghiệp</h1>
                <p>Xây dựng PC Gaming, Workstation và các linh kiện chất lượng cao</p>
                <a href="#" class="cta-button">Khám Phá Sản Phẩm</a>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="products-section">
        <div class="container">
            <h2 class="section-title">Sản Phẩm Nổi Bật</h2>
            
            <div class="product-grid">
                <div class="product-card">
                    <div class="product-image">Hình ảnh sản phẩm</div>
                    <div class="product-info">
                        <h3 class="product-title">PC Gaming RTX 5070 - i5 12400F</h3>
                        <div class="product-price">25,990,000 VNĐ</div>
                        <button class="add-to-cart">Thêm Vào Giỏ</button>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-image">Hình ảnh sản phẩm</div>
                    <div class="product-info">
                        <h3 class="product-title">Intel Core i7-14700K</h3>
                        <div class="product-price">12,990,000 VNĐ</div>
                        <button class="add-to-cart">Thêm Vào Giỏ</button>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-image">Hình ảnh sản phẩm</div>
                    <div class="product-info">
                        <h3 class="product-title">RTX 5070 Ti Super 16GB</h3>
                        <div class="product-price">28,990,000 VNĐ</div>
                        <button class="add-to-cart">Thêm Vào Giỏ</button>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-image">Hình ảnh sản phẩm</div>
                    <div class="product-info">
                        <h3 class="product-title">Monitor Gaming ASUS ROG 27" 165Hz</h3>
                        <div class="product-price">8,990,000 VNĐ</div>
                        <button class="add-to-cart">Thêm Vào Giỏ</button>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="#" class="cta-button">Xem Tất Cả Sản Phẩm</a>
            </div>
        </div>
    </section>

    <!-- PC Builder Section -->
    <section class="pc-builder-section">
        <div class="container">
            <h2 class="section-title">Công Cụ Xây Dựng PC</h2>
            <p style="font-size: 18px; margin-bottom: 30px; color: #7f8c8d;">
                Tạo cấu hình PC phù hợp với nhu cầu và ngân sách của bạn
            </p>
            <a href="#" class="cta-button">Bắt Đầu Xây Dựng PC</a>
        </div>
    </section>
</main>

<footer class="site-footer">
    <div class="container">
        <div class="footer-content">
            <div class="footer-section">
                <h3>PC Market</h3>
                <p>Chuyên cung cấp máy tính, linh kiện PC và gaming gear chất lượng cao với giá cả cạnh tranh nhất thị trường.</p>
            </div>

            <div class="footer-section">
                <h3>Danh Mục Sản Phẩm</h3>
                <ul>
                    <li><a href="#">PC Gaming</a></li>
                    <li><a href="#">PC Workstation</a></li>
                    <li><a href="#">Linh Kiện PC</a></li>
                    <li><a href="#">Màn Hình</a></li>
                    <li><a href="#">Gaming Gear</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Hỗ Trợ Khách Hàng</h3>
                <ul>
                    <li><a href="#">Hướng dẫn mua hàng</a></li>
                    <li><a href="#">Chính sách bảo hành</a></li>
                    <li><a href="#">Chính sách đổi trả</a></li>
                    <li><a href="#">Phương thức thanh toán</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Thông Tin Liên Hệ</h3>
                <p><strong>Showroom Hà Nội:</strong><br>
                83-85 Thái Hà, Trung Liệt, Đống Đa<br>
                📞 036.625.8142</p>
                
                <p style="margin-top: 15px;"><strong>Hotline:</strong> 087.997.9997<br>
                <strong>Email:</strong> <EMAIL></p>
            </div>
        </div>

        <div class="footer-bottom">
            <p>&copy; 2025 PC Market. Tất cả quyền được bảo lưu.</p>
        </div>
    </div>
</footer>

</body>
</html>
