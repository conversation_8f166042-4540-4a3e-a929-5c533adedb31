# PC Market - WordPress Ecommerce Website

A comprehensive WordPress ecommerce website inspired by pcmarket.vn, featuring PC building tools, product catalogs, and Vietnamese localization.

## Features

### 🖥️ Core Functionality
- **WordPress + WooCommerce** - Full ecommerce platform
- **Custom Theme** - Responsive design based on pcmarket.vn
- **Vietnamese Language** - Full Vietnamese localization
- **Product Categories** - Comprehensive PC component categories
- **PC Builder Tool** - Interactive PC configuration system
- **Mobile Responsive** - Optimized for all devices

### 🛍️ Ecommerce Features
- Product catalog with detailed specifications
- Shopping cart and checkout
- Price filtering and search
- Product reviews and ratings
- Inventory management
- Multiple payment methods
- Shipping configuration

### 🔧 PC Builder Tool
- Interactive component selection
- Compatibility checking
- Power consumption calculation
- Preset build configurations
- Price calculation
- Save and share builds

### 📱 Product Categories
- **PC Gaming & Streaming** - Complete gaming systems
- **PC Workstation** - Professional workstations
- **Components** - Individual PC parts
  - CPU (Intel & AMD)
  - GPU/VGA (NVIDIA & AMD)
  - RAM (DDR4 & DDR5)
  - Motherboards
  - Storage (SSD)
  - Power Supplies
  - Cases
- **Monitors** - Gaming and office displays
- **Gaming Gear** - Keyboards, mice, headsets

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- WordPress 6.0 or higher
- WooCommerce plugin

### Setup Instructions

1. **Download WordPress**
   ```bash
   curl -O https://wordpress.org/latest.tar.gz
   tar -xzf latest.tar.gz
   ```

2. **Configure Database**
   - Create a MySQL database
   - Update `wp-config.php` with your database credentials

3. **Install WooCommerce**
   - Download WooCommerce plugin
   - Activate through WordPress admin

4. **Setup Theme**
   - Copy `pcmarket-theme` to `wp-content/themes/`
   - Activate the theme in WordPress admin

5. **Import Sample Data**
   ```bash
   php setup-categories.php
   php sample-products.php
   ```

6. **Configure WooCommerce**
   - Run WooCommerce setup wizard
   - Configure payment methods
   - Set up shipping zones

### Development Server

For local development, you can use PHP's built-in server:

```bash
php -S localhost:8000
```

Or use Python's HTTP server for static testing:

```bash
python3 -m http.server 8000
```

## File Structure

```
pc-builder/
├── wp-content/
│   └── themes/
│       └── pcmarket-theme/
│           ├── style.css              # Main stylesheet
│           ├── functions.php          # Theme functions
│           ├── index.php             # Homepage template
│           ├── header.php            # Header template
│           ├── footer.php            # Footer template
│           ├── single-product.php    # Product page template
│           ├── woocommerce.php       # WooCommerce template
│           ├── page-pc-builder.php   # PC Builder tool
│           └── assets/
│               ├── js/
│               │   └── main.js       # JavaScript functionality
│               └── images/           # Theme images
├── setup-categories.php             # Category setup script
├── sample-products.php              # Sample products script
├── sample-data.sql                  # Sample data SQL
├── wp-config.php                    # WordPress configuration
└── README.md                        # This file
```

## Key Features Implementation

### PC Builder Tool
- Located at `/pc-builder` page
- Interactive component selection
- Real-time compatibility checking
- Price calculation
- Preset configurations
- Local storage for saving builds

### Product Filtering
- Price range filtering
- Category filtering
- Brand filtering
- Stock availability filtering
- AJAX-powered for smooth experience

### Vietnamese Localization
- All text in Vietnamese
- Vietnamese currency (VNĐ)
- Local business information
- Vietnamese product names and descriptions

### Responsive Design
- Mobile-first approach
- Flexible grid layouts
- Touch-friendly interfaces
- Optimized images

## Customization

### Adding New Products
1. Use WordPress admin or WooCommerce interface
2. Add product specifications in custom fields
3. Assign to appropriate categories
4. Set pricing in Vietnamese Dong

### Modifying PC Builder
1. Edit `page-pc-builder.php` for layout changes
2. Update JavaScript in `assets/js/main.js` for functionality
3. Modify compatibility data in the script

### Theme Customization
1. Edit `style.css` for visual changes
2. Modify template files for layout changes
3. Update `functions.php` for new functionality

## Sample Data

The website includes sample data based on pcmarket.vn:

### Sample Products
- PC Gaming systems (RTX 5070, RTX 5060 Ti builds)
- Individual components (CPUs, GPUs, RAM, etc.)
- Monitors (Gaming displays)
- Gaming gear (Keyboards, mice)

### Categories
- Complete category structure matching pcmarket.vn
- Hierarchical organization
- Vietnamese names and descriptions

### Pricing
- All prices in Vietnamese Dong (VNĐ)
- Realistic pricing based on current market
- Sale prices for featured products

## Performance Optimization

### Implemented Optimizations
- Minified CSS and JavaScript
- Optimized images
- Efficient database queries
- Caching-friendly structure
- Mobile optimization

### Recommended Plugins
- **WP Rocket** - Caching
- **Smush** - Image optimization
- **Yoast SEO** - SEO optimization
- **WooCommerce** - Ecommerce functionality

## Security Features

- WordPress security best practices
- Input sanitization
- CSRF protection
- SQL injection prevention
- XSS protection

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Phone: ************
- Address: 83-85 Thái Hà, Trung Liệt, Đống Đa, Hà Nội

## Acknowledgments

- Inspired by pcmarket.vn
- Built with WordPress and WooCommerce
- Uses modern web technologies
- Vietnamese market focused

---

**Note**: This is a demonstration project. For production use, ensure proper security measures, performance optimization, and legal compliance.
